#!/usr/bin/env python3
"""
AWS API Gateway Funds API Test Client

This script tests the AWS API Gateway endpoints for fund management.
Requires authentication with AWS Cognito.

Usage:
    python test_aws_api_client.py

Requirements:
    pip install requests boto3 python-dotenv
"""

import json
import os
from dotenv import load_dotenv
import requests
import boto3
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import sys
from dataclasses import dataclass
import jwt

# Get the directory of the current script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))


@dataclass
class APIConfig:
    """Configuration for API testing"""

    base_url: str = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    region: str = "ap-northeast-1"
    user_pool_id: str = "ap-northeast-1_H2kKHGUAT"
    client_id: str = "2jh76f894g6lv9vrus4qbb9hu7"  # CloudFormation client ID
    test_email: str = "<EMAIL>"
    test_password: str = "TestPassword123!"

    # API Gateway configuration
    api_gateway_id: str = "b5uqilw5yk"
    authorizer_id: str = "t4sy7c"


class FundsAPIClient:
    """Client for testing AWS API Gateway Funds API"""

    def __init__(self, config: APIConfig):
        # load env vars from .env_test using absolute path
        env_file = os.path.join(SCRIPT_DIR, ".env_test")
        # Load environment variables
        load_dotenv(dotenv_path=env_file, override=True)

        print("=" * 60)
        print("🔍 Environment variables after load_dotenv:")
        print("=" * 60)
        print(f"AWS_SECRET_ACCESS_KEY: {os.getenv('AWS_SECRET_ACCESS_KEY')}")
        print(f"AWS_ACCESS_KEY_ID: {os.getenv('AWS_ACCESS_KEY_ID')}")
        print(f"AWS_REGION: {os.getenv('AWS_REGION')}")
        print(f"AWS_DEFAULT_REGION: {os.getenv('AWS_DEFAULT_REGION')}")
        print("=" * 60)

        self.config = config
        self.session = requests.Session()
        self.access_token: Optional[str] = None
        self.cognito_client = boto3.client("cognito-idp", region_name=config.region)

    def check_client_configuration(self):
        """Check and display client configuration details"""
        try:
            print("\n🔍 Checking client configuration...")
            client_details = self.cognito_client.describe_user_pool_client(
                UserPoolId=self.config.user_pool_id, ClientId=self.config.client_id
            )

            client_info = client_details.get("UserPoolClient", {})
            has_secret = bool(client_info.get("ClientSecret"))
            client_type = "Confidential" if has_secret else "Public"

            print(f"   📋 Client ID: {self.config.client_id}")
            print(f"   🔐 Client Type: {client_type}")
            print(f"   🔑 Has Secret: {'Yes' if has_secret else 'No'}")
            print(f"   📝 Client Name: {client_info.get('ClientName', 'N/A')}")

            # Check supported auth flows
            auth_flows = client_info.get("ExplicitAuthFlows", [])
            print(f"   🔄 Supported Auth Flows:")
            for flow in auth_flows:
                print(f"      • {flow}")

            # Check OAuth settings
            oauth_flows = client_info.get("AllowedOAuthFlows", [])
            if oauth_flows:
                print(f"   🌐 OAuth Flows:")
                for flow in oauth_flows:
                    print(f"      • {flow}")

            oauth_scopes = client_info.get("AllowedOAuthScopes", [])
            if oauth_scopes:
                print(f"   📜 OAuth Scopes:")
                for scope in oauth_scopes:
                    print(f"      • {scope}")

            return {
                "client_type": client_type.lower(),
                "has_secret": has_secret,
                "auth_flows": auth_flows,
                "oauth_flows": oauth_flows,
                "oauth_scopes": oauth_scopes,
            }

        except Exception as e:
            print(f"   ❌ Error checking client configuration: {str(e)}")
            return None

    def check_and_fix_authorizer(self):
        """Check API Gateway authorizer configuration and fix if needed"""
        try:
            print("\n🔧 Checking API Gateway authorizer configuration...")

            # Create API Gateway client
            apigateway_client = boto3.client(
                "apigateway", region_name=self.config.region
            )

            # Get current authorizer configuration
            authorizer = apigateway_client.get_authorizer(
                restApiId=self.config.api_gateway_id,
                authorizerId=self.config.authorizer_id,
            )

            current_user_pool_arn = authorizer.get("providerARNs", [])
            if current_user_pool_arn:
                current_user_pool_id = current_user_pool_arn[0].split("/")[-1]
                print(f"   📋 Current authorizer user pool: {current_user_pool_id}")
                print(f"   📋 Expected user pool: {self.config.user_pool_id}")

                if current_user_pool_id != self.config.user_pool_id:
                    print(f"   ⚠️  Authorizer user pool mismatch detected!")
                    print(f"   🔧 Attempting to fix authorizer configuration...")

                    # Get the current authorizer details to preserve settings
                    current_authorizer = apigateway_client.get_authorizer(
                        restApiId=self.config.api_gateway_id,
                        authorizerId=self.config.authorizer_id,
                    )

                    # Delete the current authorizer
                    print(f"   🗑️  Deleting current authorizer...")
                    apigateway_client.delete_authorizer(
                        restApiId=self.config.api_gateway_id,
                        authorizerId=self.config.authorizer_id,
                    )

                    # Create a new authorizer with the correct user pool
                    correct_user_pool_arn = f"arn:aws:cognito-idp:{self.config.region}:719290418828:userpool/{self.config.user_pool_id}"

                    print(f"   ➕ Creating new authorizer with correct user pool...")
                    new_authorizer = apigateway_client.create_authorizer(
                        restApiId=self.config.api_gateway_id,
                        name=current_authorizer["name"],
                        type=current_authorizer["type"],
                        providerARNs=[correct_user_pool_arn],
                        authType=current_authorizer.get(
                            "authType", "cognito_user_pools"
                        ),
                        identitySource=current_authorizer.get(
                            "identitySource", "method.request.header.Authorization"
                        ),
                    )

                    print(
                        f"   ✅ New authorizer created with ID: {new_authorizer['id']}"
                    )
                    print(
                        f"   ✅ Authorizer updated to use user pool: {self.config.user_pool_id}"
                    )

                    # Deploy the API to make changes effective
                    print(f"   🚀 Deploying API changes...")
                    apigateway_client.create_deployment(
                        restApiId=self.config.api_gateway_id,
                        stageName="dev",
                        description="Updated authorizer user pool configuration",
                    )
                    print(f"   ✅ API deployment completed")

                    # Update the config with the new authorizer ID
                    self.config.authorizer_id = new_authorizer["id"]

                    return True
                else:
                    print(f"   ✅ Authorizer configuration is correct")
                    return False
            else:
                print(f"   ❌ No user pool ARN found in authorizer")
                return False

        except Exception as e:
            print(f"   ❌ Error checking/fixing authorizer: {str(e)}")
            return False

    def authenticate(self) -> bool:
        """Authenticate with AWS Cognito and get access token"""
        try:
            print("🔐 Authenticating with AWS Cognito...")

            # Try client credentials flow first (for machine-to-machine)
            try:
                print("   Checking client type...")

                # First, we need to get the client details to check type
                client_details = self.cognito_client.describe_user_pool_client(
                    UserPoolId=self.config.user_pool_id, ClientId=self.config.client_id
                )

                client_secret = client_details.get("UserPoolClient", {}).get(
                    "ClientSecret"
                )
                client_type = "confidential" if client_secret else "public"

                print(f"   📋 Client type detected: {client_type}")

                if client_secret:
                    print("   Trying CLIENT_CREDENTIALS flow...")
                    # Use OAuth2 client credentials flow
                    import base64
                    import requests

                    # Encode client credentials
                    credentials = f"{self.config.client_id}:{client_secret}"
                    encoded_credentials = base64.b64encode(
                        credentials.encode()
                    ).decode()

                    # Token endpoint for Cognito
                    token_url = f"https://{self.config.user_pool_id.split('_')[0]}.auth.{self.config.region}.amazoncognito.com/oauth2/token"

                    headers = {
                        "Authorization": f"Basic {encoded_credentials}",
                        "Content-Type": "application/x-www-form-urlencoded",
                    }

                    data = {
                        "grant_type": "client_credentials",
                        "scope": "fundflow-api/admin:all",  # Request all scopes
                    }

                    oauth_response = requests.post(
                        token_url, headers=headers, data=data
                    )

                    if oauth_response.status_code == 200:
                        token_data = oauth_response.json()
                        self.access_token = token_data["access_token"]
                        self.session.headers.update(
                            {
                                "Authorization": f"Bearer {self.access_token}",
                                "Content-Type": "application/json",
                            }
                        )

                        print("   ✅ CLIENT_CREDENTIALS authentication successful!")
                        print(
                            f"   Token type: {token_data.get('token_type', 'Bearer')}"
                        )
                        print(
                            f"   Expires in: {token_data.get('expires_in', 'N/A')} seconds"
                        )
                        print(f"   Scope: {token_data.get('scope', 'N/A')}")

                        return True
                    else:
                        print(
                            f"   ❌ CLIENT_CREDENTIALS failed: {oauth_response.status_code} - {oauth_response.text}"
                        )
                else:
                    print(
                        "   ℹ️  Public client detected - skipping CLIENT_CREDENTIALS flow"
                    )
                    print("   🔄 Will try user authentication flows...")

            except Exception as e:
                print(f"   ❌ CLIENT_CREDENTIALS check error: {str(e)}")

            # Try different authentication flows for user authentication
            auth_flows = [
                "USER_PASSWORD_AUTH",
                "ADMIN_USER_PASSWORD_AUTH",
                "ALLOW_ADMIN_USER_PASSWORD_AUTH",
                "ALLOW_USER_PASSWORD_AUTH",
            ]

            for auth_flow in auth_flows:
                try:
                    print(f"   Trying {auth_flow} flow...")

                    if auth_flow.startswith("ADMIN_"):
                        # Use admin authentication
                        response = self.cognito_client.admin_initiate_auth(
                            UserPoolId=self.config.user_pool_id,
                            ClientId=self.config.client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.config.test_email,
                                "PASSWORD": self.config.test_password,
                            },
                        )
                    else:
                        # Use regular authentication
                        response = self.cognito_client.initiate_auth(
                            ClientId=self.config.client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.config.test_email,
                                "PASSWORD": self.config.test_password,
                            },
                        )

                    if "AuthenticationResult" in response:
                        auth_result = response["AuthenticationResult"]
                        access_token = auth_result["AccessToken"]
                        id_token = auth_result.get("IdToken")
                        # Test access token first (UNCOMMENT THIS SECTION)
                        self.access_token = access_token
                        print(f"   🔄 Access token: {self.access_token}")
                        self.session.headers.update(
                            {
                                "Authorization": f"Bearer {self.access_token}",
                                "Content-Type": "application/json",
                            }
                        )

                        test_response = self._test_token_with_api()
                        if test_response["works"]:
                            print("   ✅ Access token works with API!")
                            return True
                        else:
                            print(
                                f"   ❌ Access token failed: {test_response['status']}"
                            )

                        # Only test ID token if access token failed
                        if id_token:
                            print("   🔄 Trying ID token...")
                            self.access_token = id_token
                            print(f"   🔄 ID token: {self.access_token}")
                            self.session.headers.update(
                                {
                                    "Authorization": f"Bearer {self.access_token}",
                                    "Content-Type": "application/json",
                                }
                            )
                            test_response = self._test_token_with_api()
                            if test_response["works"]:
                                print("   ✅ ID token works with API!")
                                return True
                            else:
                                print(
                                    f"   ❌ ID token failed: {test_response['status']}"
                                )

                        # If neither token works, but authentication was successful,
                        # there might be a backend configuration issue
                        print("   ⚠️  Authentication succeeded but API access failed")
                        print("   ℹ️  This might be a backend configuration issue")

                        # Keep the access token as it's the standard one
                        self.access_token = access_token
                        self.session.headers.update(
                            {
                                "Authorization": f"Bearer {self.access_token}",
                                "Content-Type": "application/json",
                            }
                        )
                        return True  # Return True since authentication worked

                except Exception as flow_error:
                    print(f"   ❌ {auth_flow} failed: {str(flow_error)}")
                    continue

            print("❌ All authentication flows failed")
            return False

        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False

    def _test_token_with_api(self) -> Dict[str, Any]:
        """Test if current token works with API"""
        try:
            response = self.session.get(f"{self.config.base_url}/funds", timeout=5)
            return {
                "works": response.status_code not in [401, 403],
                "status": response.status_code,
                "response": response.text[:100] if response.text else "",
            }
        except Exception as e:
            return {"works": False, "status": 0, "response": str(e)}

    def get_token_from_existing_session(self) -> bool:
        """Try to get token from existing AWS session/credentials"""
        try:
            print("🔍 Attempting to use existing AWS session...")

            # Try to get token using AWS STS
            sts_client = boto3.client("sts", region_name=self.config.region)
            identity = sts_client.get_caller_identity()
            print(f"   Current AWS identity: {identity.get('Arn', 'Unknown')}")

            # For now, we'll use a placeholder approach
            # In a real scenario, you might have a token service or use AWS SSO
            print("   ⚠️  No direct token extraction method available")
            return False

        except Exception as e:
            print(f"   ❌ Could not use existing session: {str(e)}")
            return False

    def authenticate_with_scope(self, scope: str = "fundflow-api/admin:all") -> bool:
        """Authenticate using OAuth2 client credentials with specific scope"""
        try:
            print(f"🔑 Authenticating with scope: {scope}")

            # Get client details to check if it's confidential or public
            client_details = self.cognito_client.describe_user_pool_client(
                UserPoolId=self.config.user_pool_id, ClientId=self.config.client_id
            )

            client_secret = client_details.get("UserPoolClient", {}).get("ClientSecret")
            client_type = "confidential" if client_secret else "public"

            print(f"   📋 Client type detected: {client_type}")

            if not client_secret:
                print(
                    "   ℹ️  Public client detected - client credentials flow not available"
                )
                print("   🔄 Falling back to user authentication flow...")

                # For public clients, we need to use user-based authentication
                # Try the standard authenticate method first
                if self.authenticate():
                    print("   ✅ Public client authentication successful!")
                    return True
                else:
                    print("   ❌ Public client authentication failed")
                    print(
                        "   💡 Try using authenticate_with_manual_token() for manual token input"
                    )
                    return False

            import base64
            import requests

            # Encode client credentials for confidential clients
            credentials = f"{self.config.client_id}:{client_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()

            # Token endpoint for Cognito
            token_url = f"https://{self.config.user_pool_id.split('_')[0]}.auth.{self.config.region}.amazoncognito.com/oauth2/token"

            headers = {
                "Authorization": f"Basic {encoded_credentials}",
                "Content-Type": "application/x-www-form-urlencoded",
            }

            data = {"grant_type": "client_credentials", "scope": scope}

            response = requests.post(token_url, headers=headers, data=data)

            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data["access_token"]
                self.session.headers.update(
                    {
                        "Authorization": f"Bearer {self.access_token}",
                        "Content-Type": "application/json",
                    }
                )

                print("   ✅ Confidential client authentication successful!")
                print(f"   Token type: {token_data.get('token_type', 'Bearer')}")
                print(f"   Expires in: {token_data.get('expires_in', 'N/A')} seconds")
                print(f"   Granted scope: {token_data.get('scope', 'N/A')}")

                # Decode and display token claims for debugging
                try:
                    decoded = jwt.decode(
                        self.access_token, options={"verify_signature": False}
                    )
                    print(
                        f"   Token claims: {json.dumps(decoded, indent=2, default=str)}"
                    )
                except Exception as e:
                    print(f"   Token decode error: {e}")

                return True
            else:
                print(
                    f"   ❌ Confidential client authentication failed: {response.status_code} - {response.text}"
                )
                return False

        except Exception as e:
            print(f"   ❌ Scope authentication error: {str(e)}")
            return False

    def authenticate_with_manual_token(self) -> bool:
        """Allow manual token input for testing"""
        try:
            print("\n🔑 Manual token input option:")
            print("You can get a token by:")
            print("1. Logging into the frontend (http://localhost:3000)")
            print("2. Opening browser DevTools > Application > Local Storage")
            print("3. Finding the JWT token")
            print()

            manual_token = input("Enter JWT token (or press Enter to skip): ").strip()

            if manual_token:
                self.access_token = manual_token
                self.session.headers.update(
                    {
                        "Authorization": f"Bearer {self.access_token}",
                        "Content-Type": "application/json",
                    }
                )
                print("✅ Manual token set! Testing authentication...")

                # Test the token with a simple API call
                test_response = self.session.get(
                    f"{self.config.base_url}/funds", timeout=10
                )
                if test_response.status_code == 401:
                    print("❌ Token appears to be invalid or expired")
                    return False
                else:
                    print("✅ Token appears to be working!")
                    return True
            else:
                print("⏭️  Skipping manual token input")
                return False

        except Exception as e:
            print(f"❌ Manual token error: {str(e)}")
            return False

    def make_request(
        self,
        method: str,
        endpoint: str,
        data: Dict[str, Any] = None,
        params: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """Make authenticated HTTP request to API"""
        url = f"{self.config.base_url}{endpoint}"

        try:
            print(f"\n📡 {method} {url}")
            if params:
                print(f"   Query params: {params}")
            if data:
                print(f"   Request body: {json.dumps(data, indent=2)}")

            response = self.session.request(
                method=method, url=url, json=data, params=params, timeout=30
            )

            print(f"   Status: {response.status_code}")

            try:
                response_data = response.json()
                print(f"   Response: {json.dumps(response_data, indent=2)}")
                return {
                    "status_code": response.status_code,
                    "data": response_data,
                    "success": 200 <= response.status_code < 300,
                }
            except json.JSONDecodeError:
                print(f"   Response (text): {response.text}")
                return {
                    "status_code": response.status_code,
                    "data": {"message": response.text},
                    "success": 200 <= response.status_code < 300,
                }

        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request error: {str(e)}")
            return {"status_code": 0, "data": {"error": str(e)}, "success": False}

    def test_list_funds(self) -> Dict[str, Any]:
        """Test GET /funds - List all funds"""
        print("\n" + "=" * 50)
        print("🧪 Testing: List Funds")
        print("=" * 50)

        # Test basic listing
        response = self.make_request("GET", "/funds")

        # Test with pagination
        # print("\n📄 Testing pagination...")
        # response_paginated = self.make_request(
        #    "GET", "/funds", params={"page": 1, "page_size": 5}
        # )

        # Test with filters
        # print("\n🔍 Testing filters...")
        # response_filtered = self.make_request(
        #    "GET",
        #    "/funds",
        #    params={"status": "active", "fund_type": "equity", "search": "HDFC"},
        # )

        return response

    def test_get_fund(self, fund_id: str = None) -> Dict[str, Any]:
        """Test GET /funds/{fund_id} - Get specific fund"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Specific Fund")
        print("=" * 50)

        # If no fund_id provided, try to get one from list
        if not fund_id:
            list_response = self.make_request("GET", "/funds", params={"page_size": 1})
            if list_response["success"] and list_response["data"].get("data", {}).get(
                "funds"
            ):
                fund_id = list_response["data"]["data"]["funds"][0]["fund_id"]
                print(f"📋 Using fund_id from list: {fund_id}")
            else:
                # Use a default fund ID that should exist
                fund_id = "FUND-001"
                print(f"📋 Using default fund_id: {fund_id}")

        response = self.make_request("GET", f"/funds/{fund_id}")

        # Test non-existent fund
        print("\n🔍 Testing non-existent fund...")
        self.make_request("GET", "/funds/NON-EXISTENT-FUND")

        return response

    def test_get_fund_details(self, fund_id: str = None) -> Dict[str, Any]:
        """Test GET /funds/{fund_id}/details - Get enriched fund details"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Fund Details (NEW ENDPOINT)")
        print("=" * 50)

        # If no fund_id provided, use default
        if not fund_id:
            fund_id = "FUND-001"
            print(f"📋 Using default fund_id: {fund_id}")

        response = self.make_request("GET", f"/funds/{fund_id}/details")

        if response["success"]:
            data = response["data"].get("data", {})
            print("\n✅ Validating fund details structure...")

            # Check if expected fields exist
            expected_fields = [
                "id",
                "name",
                "type",
                "nav",
                "analytics",
                "historicalData",
                "benchmark",
                "documents",
            ]

            missing_fields = []
            for field in expected_fields:
                if field not in data:
                    missing_fields.append(field)
                else:
                    print(f"   ✓ Found {field}")

            if missing_fields:
                print(f"   ❌ Missing fields: {missing_fields}")
            else:
                print("   ✅ All expected fields present")

            # Validate analytics structure
            if "analytics" in data:
                analytics = data["analytics"]
                analytics_fields = [
                    "kpis",
                    "riskMetrics",
                    "assetAllocation",
                    "topHoldings",
                ]
                for field in analytics_fields:
                    if field in analytics:
                        print(f"   ✓ Analytics.{field} present")
                    else:
                        print(f"   ❌ Analytics.{field} missing")

        # Test non-existent fund
        print("\n🔍 Testing non-existent fund details...")
        self.make_request("GET", "/funds/NON-EXISTENT-FUND/details")

        return response

    def test_get_fund_historical(self, fund_id: str = None) -> Dict[str, Any]:
        """Test GET /funds/{fund_id}/historical - Get historical data"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Get Fund Historical Data (NEW ENDPOINT)")
        print("=" * 50)

        # If no fund_id provided, use default
        if not fund_id:
            fund_id = "FUND-001"
            print(f"📋 Using default fund_id: {fund_id}")

        # Test different time periods
        periods = ["1D", "1W", "1M", "3M", "6M", "1Y", "3Y", "5Y"]
        results = []

        for period in periods:
            print(f"\n📊 Testing period: {period}")
            response = self.make_request(
                "GET", f"/funds/{fund_id}/historical", params={"period": period}
            )

            if response["success"]:
                data = response["data"].get("data", [])
                print(f"   ✅ Got {len(data)} data points for {period}")

                # Validate data structure
                if data and isinstance(data, list):
                    first_point = data[0]
                    expected_fields = ["date", "value", "nav"]
                    for field in expected_fields:
                        if field in first_point:
                            print(f"   ✓ Data point has {field}")
                        else:
                            print(f"   ❌ Data point missing {field}")
                else:
                    print(f"   ❌ Invalid data format for {period}")
            else:
                print(f"   ❌ Failed to get data for {period}")

            results.append((period, response["success"]))

        # Test invalid period
        print("\n🔍 Testing invalid period...")
        self.make_request(
            "GET", f"/funds/{fund_id}/historical", params={"period": "INVALID"}
        )

        # Test non-existent fund
        print("\n🔍 Testing non-existent fund historical...")
        self.make_request("GET", "/funds/NON-EXISTENT-FUND/historical")

        # Return result for first successful period or last attempt
        successful_results = [r for r in results if r[1]]
        if successful_results:
            return {
                "success": True,
                "tested_periods": len(periods),
                "successful_periods": len(successful_results),
            }
        else:
            return {
                "success": False,
                "tested_periods": len(periods),
                "successful_periods": 0,
            }

    def test_create_fund(self) -> Dict[str, Any]:
        """Test POST /funds - Create new fund"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Create Fund")
        print("=" * 50)

        # Generate unique fund data
        timestamp = int(time.time())
        test_fund = {
            "fund_id": f"TEST-FUND-{timestamp}",
            "fund_name": f"Test Fund {timestamp}",
            "fund_type": "EQUITY",
            "status": "ACTIVE",
            "nav": 125.75,
            "expense_ratio": 1.25,
            "min_investment": 1000,
            "fund_manager": "Test Fund Manager",
            "inception_date": "2024-01-01",
            "description": "Test fund created by API client",
            "benchmark": "NIFTY 50",
            "risk_level": "MODERATE",
            "holdings": [
                {
                    "security_name": "Test Security 1",
                    "percentage": 15.5,
                    "sector": "Technology",
                },
                {
                    "security_name": "Test Security 2",
                    "percentage": 12.3,
                    "sector": "Banking",
                },
            ],
            "performance": {
                "1_year": 12.5,
                "3_year": 15.2,
                "5_year": 18.7,
                "inception": 20.1,
            },
        }

        response = self.make_request("POST", "/funds", data=test_fund)

        # Test duplicate creation (should fail)
        if response["success"]:
            print("\n🔄 Testing duplicate creation (should fail)...")
            self.make_request("POST", "/funds", data=test_fund)

        # Test invalid data
        print("\n❌ Testing invalid fund data...")
        invalid_fund = {
            "fund_id": f"INVALID-{timestamp}",
            "fund_name": "",  # Invalid empty name
            "nav": -100,  # Invalid negative NAV
            "expense_ratio": 150,  # Invalid high expense ratio
        }
        self.make_request("POST", "/funds", data=invalid_fund)

        return response

    def test_unauthorized_access(self) -> Dict[str, Any]:
        """Test API without authentication"""
        print("\n" + "=" * 50)
        print("🧪 Testing: Unauthorized Access")
        print("=" * 50)

        # Save current token
        original_token = self.access_token

        # Remove authorization header
        if "Authorization" in self.session.headers:
            del self.session.headers["Authorization"]

        # Try to access protected endpoints
        endpoints_to_test = [
            "/funds",
            "/funds/FUND-001",
            "/funds/FUND-001/details",
            "/funds/FUND-001/historical",
        ]

        results = []
        for endpoint in endpoints_to_test:
            print(f"\n🔒 Testing unauthorized access to {endpoint}")
            response = self.make_request("GET", endpoint)
            results.append((endpoint, response["status_code"]))

        # Restore authorization
        if original_token:
            self.session.headers["Authorization"] = f"Bearer {original_token}"

        return {
            "status_code": 401,
            "success": False,
            "tested_endpoints": len(endpoints_to_test),
        }

    def run_basic_tests(self):
        """Run basic read-only tests"""
        print("🚀 Starting Basic AWS API Gateway Tests")
        print("=" * 60)

        # Test unauthorized access first
        # self.test_unauthorized_access()

        # Test read operations
        list_result = self.test_list_funds()
        # get_result = self.test_get_fund()
        # details_result = self.test_get_fund_details()
        # historical_result = self.test_get_fund_historical()

        # Summary for basic tests
        print("\n" + "=" * 60)
        print("📊 BASIC TEST SUMMARY")
        print("=" * 60)

        tests = [
            ("List Funds", list_result["success"]),
            # ("Get Fund", get_result["success"]),
            # ("Get Fund Details", details_result["success"]),
            # ("Get Historical Data", historical_result["success"]),
        ]

        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:20} {status}")

        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        print(f"\nBasic Tests: {passed}/{total} passed")

        # Special note for historical data
        # if (
        #    isinstance(historical_result, dict)
        #    and "tested_periods" in historical_result
        # ):
        #    print(
        #        f"Historical Data: {historical_result['successful_periods']}/{historical_result['tested_periods']} periods successful"
        #    )

    def run_comprehensive_test(self):
        """Run all API tests including write operations"""
        print("🚀 Starting Comprehensive AWS API Gateway Tests")
        print("=" * 60)

        # Test unauthorized access
        self.test_unauthorized_access()

        # Test read operations
        list_result = self.test_list_funds()
        get_result = self.test_get_fund()
        details_result = self.test_get_fund_details()
        historical_result = self.test_get_fund_historical()

        # Test write operations
        create_result = self.test_create_fund()

        # Summary
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)

        tests = [
            ("List Funds", list_result["success"]),
            ("Get Fund", get_result["success"]),
            ("Get Fund Details", details_result["success"]),
            ("Get Historical Data", historical_result["success"]),
            ("Create Fund", create_result["success"]),
        ]

        for test_name, success in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:20} {status}")

        passed = sum(1 for _, success in tests if success)
        total = len(tests)
        print(f"\nOverall: {passed}/{total} tests passed")

        # Special note for historical data
        if (
            isinstance(historical_result, dict)
            and "tested_periods" in historical_result
        ):
            print(
                f"Historical Data: {historical_result['successful_periods']}/{historical_result['tested_periods']} periods successful"
            )


def main():
    """Main function to run the test client"""
    try:
        config = APIConfig()
        client = FundsAPIClient(config)

        # Check client configuration first
        print("=" * 60)
        print("🔧 AWS COGNITO CLIENT CONFIGURATION")
        print("=" * 60)
        client_info = client.check_client_configuration()

        # Check and fix API Gateway authorizer if needed
        print("\n" + "=" * 60)
        print("🔧 API GATEWAY AUTHORIZER CHECK")
        print("=" * 60)
        authorizer_fixed = client.check_and_fix_authorizer()

        if authorizer_fixed:
            print("   ⏳ Waiting 10 seconds for API Gateway deployment to propagate...")
            import time

            time.sleep(10)

        print("\n" + "=" * 60)
        print("🔐 AUTHENTICATION ATTEMPTS")
        print("=" * 60)

        # Try multiple authentication methods
        authenticated = False

        # Method 1: Try OAuth2 client credentials with resource server scope
        if client.authenticate_with_scope("fundflow-api/admin:all"):
            authenticated = True

        # Method 2: Try Cognito authentication
        elif client.authenticate():
            authenticated = True

        # Method 3: Try existing AWS session
        elif client.get_token_from_existing_session():
            authenticated = True

        # Method 4: Manual token input
        elif client.authenticate_with_manual_token():
            authenticated = True

        if authenticated:
            print("\n🎯 Choose test level:")
            print("1. Basic tests (read-only, includes NEW endpoints)")
            print("2. Comprehensive tests (includes writes)")
            print(
                "\n💡 Note: Tests now include the new /funds/{id}/details and /funds/{id}/historical endpoints"
            )
            print("   If tests show 401/502 errors despite successful authentication,")
            print(
                "   this may indicate backend environment variable configuration issues."
            )

            # choice = input("\nEnter choice (1 or 2, default=1): ").strip()

            # if choice == "2":
            #    client.run_comprehensive_test()
            # else:
            client.run_basic_tests()
        else:
            print(
                "\n❌ Could not authenticate. Running unauthorized access test only..."
            )
            client.test_unauthorized_access()

    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
