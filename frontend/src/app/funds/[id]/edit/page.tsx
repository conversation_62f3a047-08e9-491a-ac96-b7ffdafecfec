'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { FundDetails, Fund } from '@/types';
import { Card, Button } from '@/components/ui';
import FundEditForm from '@/components/funds/FundEditForm';
import { safeINR, safeUSD, safeToFixed } from '@/utils';

export default function FundEditPage() {
  const params = useParams();
  const router = useRouter();
  const fundId = params.id as string;
  const { t } = useTranslation();
  
  const [fundDetails, setFundDetails] = useState<FundDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    fetchFundDetails();
  }, [fundId]);

  const fetchFundDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fundApi.getFundDetails(fundId);
      setFundDetails(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch fund details');
      console.error('Failed to fetch fund details:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveFund = async (fundData: Partial<Fund>) => {
    try {
      setSaveLoading(true);
      setError(null);
      
      console.log('Saving fund data:', fundData);
      
      // Call API to update fund
      const response = await fundApi.updateFund(fundId, fundData);
      
      console.log('Fund updated successfully:', response.data);
      
      // Show success message and redirect
      alert(t('funds.fundUpdatedSuccessfully'));
      router.push(`/funds/${fundId}`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update fund';
      setError(errorMessage);
      console.error('Failed to update fund:', err);
      alert(t('funds.errorUpdatingFund', { error: errorMessage }));
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/funds/${fundId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('funds.loadingFunds')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <Card.Content>
            <div className="text-center">
              <h3 className="text-lg font-medium text-red-600 mb-2">{t('common.error')}</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={fetchFundDetails} variant="outline">
                  {t('funds.tryAgain')}
                </Button>
                <Button onClick={() => router.push('/funds')}>
                  {t('funds.backToFunds')}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (!fundDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <Card.Content>
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-800 mb-2">{t('funds.fundNotFound')}</h3>
              <p className="text-gray-600 mb-4">{t('funds.fundNotFoundMessage')}</p>
              <Button onClick={() => router.push('/funds')}>
                {t('funds.backToFunds')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors" style={{ fontFamily: 'Lato, sans-serif' }}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button 
              variant="outline" 
              onClick={handleCancel}
              className="flex items-center gap-2"
            >
              ← {t('common.back')}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {t('funds.editFundTitle', { name: fundDetails.name })}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {fundDetails.symbol} • {fundDetails.type.replace('_', ' ').toUpperCase()}
              </p>
            </div>
          </div>
        </div>

        {/* Edit Form */}
        <Card>
          <Card.Header>
            <Card.Title>{t('funds.fundDetails')}</Card.Title>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('funds.form.updateInfo')}
            </p>
          </Card.Header>
          <Card.Content>
            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <p className="text-red-600 dark:text-red-300 text-sm">{error}</p>
              </div>
            )}
            
            <FundEditForm
              fund={fundDetails}
              isEditing={true}
              onSubmit={handleSaveFund}
              onCancel={handleCancel}
              loading={saveLoading}
            />
          </Card.Content>
        </Card>

        {/* Current Fund Info for Reference */}
        <Card className="mt-6">
          <Card.Header>
            <Card.Title>{t('funds.currentFundInformation')}</Card.Title>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('funds.referenceFundInfo')}
            </p>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.currentNAV')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{safeUSD(fundDetails.nav)}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.aum')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">${safeToFixed(fundDetails.aum ? fundDetails.aum / 1000000 : null, 1)}M</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.expenseRatio')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.expenseRatio}%</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.riskLevel')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100 capitalize">{fundDetails.riskLevel}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.fundManager')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.fundManager}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm text-gray-600 dark:text-gray-400">{t('funds.rating')}</div>
                <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">{fundDetails.rating}/5 ⭐</div>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
} 