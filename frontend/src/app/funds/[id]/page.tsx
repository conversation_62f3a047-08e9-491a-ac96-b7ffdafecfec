'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/i18n/provider';
import { fundApi } from '@/lib/api';
import { FundDetails, TimePeriod, ChartDataPoint } from '@/types';
import { Card, Button, Tabs } from '@/components/ui';
import { safeINR, safeUSD, safePercentage, safePercentageChange, formatPercentage, safeCurrencyChange, safeCurrencyChangeUSD, safeToFixed, transformHoldingsData } from '@/utils';
import FundKPIs from '@/components/funds/FundKPIs';
import FundBasicInfo from '@/components/funds/FundBasicInfo';
import FundAllocation from '@/components/funds/FundAllocation';
import PerformanceChart from '@/components/funds/PerformanceChart';
import AllocationChart from '@/components/funds/AllocationChart';
import HistoricalAnalysis from '@/components/funds/HistoricalAnalysis';
import MarketDataDisplay from '@/components/funds/MarketDataDisplay';
import RiskAnalyticsDisplay from '@/components/funds/RiskAnalyticsDisplay';
import NewsFeed from '@/components/funds/NewsFeed';
import Documents from '@/components/funds/Documents';
import {
  ErrorOutline,
  Star,
  StarBorder,
  BarChart,
  Refresh,
  Add,
  Business
} from '@mui/icons-material';

export default function FundDetailsPage() {
  const params = useParams();
  const fundId = params.id as string;
  const { t } = useTranslation();
  
  const [fundDetails, setFundDetails] = useState<FundDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);  
  const [chartLoading, setChartLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('1Y');
  const [historicalData, setHistoricalData] = useState<ChartDataPoint[]>([]);

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000000) {
      return `$${(amount / 1000000000).toFixed(1)}B`;
    } else if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  useEffect(() => {
    if (fundId) {
      fetchFundDetails();
    }
  }, [fundId]);

  const fetchFundDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fundApi.getFundDetails(fundId);
      setFundDetails(response.data);
      setHistoricalData(response.data.historicalData); // Set initial historical data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch fund details');
    } finally {
      setLoading(false);
    }
  };

  // Fetch historical data for a specific time period
  const fetchHistoricalData = async (period: TimePeriod) => {
    try {
      setChartLoading(true);
      const response = await fundApi.getFundHistoricalData(fundId, period);
      setHistoricalData(response.data);
    } catch (err) {
      console.error('Failed to fetch historical data:', err);
      // Keep existing data on error
    } finally {
      setChartLoading(false);
    }
  };

  // Handle time period changes
  const handlePeriodChange = async (period: TimePeriod) => {
    setSelectedPeriod(period);
    await fetchHistoricalData(period);
  };

  const handleRefresh = () => {
    fetchFundDetails();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">{t('funds.loadingFunds')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
          <Card.Content>
            <div className="text-center">
              <ErrorOutline className="text-red-600 dark:text-red-400 text-4xl mb-4" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">{t('funds.errorLoadingFunds')}</h3>
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
              <div className="flex space-x-3 justify-center">
                <Button variant="outline" onClick={handleRefresh}>
                  <Refresh className="w-4 h-4 mr-2" />
                  {t('funds.retry')}
                </Button>
                <Button onClick={() => window.history.back()}>
                  {t('funds.goBack')}
                </Button>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (!fundDetails) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <Card.Content>
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">{t('funds.fundNotFound')}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{t('funds.fundNotFoundMessage')}</p>
              <Button onClick={() => window.history.back()}>
                {t('funds.goBack')}
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  // Transform holdings data from backend format to frontend format
  const transformedHoldingsData = transformHoldingsData(fundDetails);

  // Debug logging to see what data we have
  console.log('🔍 Fund details analytics:', fundDetails.analytics);
  console.log('🔍 Transformed holdings data:', transformedHoldingsData);

  // Define tabs configuration
  const tabsConfig = [
    {
      id: 'overview',
      label: t('funds.overview'),
      children: (
        <div className="space-y-6">
          {/* Current Performance Summary */}
          <Card>
            <Card.Header>
              <Card.Title>{t('funds.currentPerformance')}</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6" role="list" aria-label="Fund performance metrics">
                <div role="listitem" className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                  <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${t('funds.currentNAV')}: ${safeToFixed(fundDetails.nav)} dollars`}>
                    {safeUSD(fundDetails.nav)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">{t('funds.currentNAV')}</div>
                  <div 
                    className={`text-sm font-medium mt-1 ${
                      fundDetails.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}
                    aria-label={`${t('funds.change')}: ${fundDetails.changePercent >= 0 ? 'positive' : 'negative'} ${Math.abs(fundDetails.changePercent ?? 0).toFixed(2)} percent, ${Math.abs(fundDetails.change ?? 0).toFixed(2)} dollars`}
                  >
                    {safePercentageChange(fundDetails.changePercent)} 
                    ({safeCurrencyChangeUSD(fundDetails.change)})
                  </div>
                </div>
                
                <div role="listitem" className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                  <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${t('funds.aum')}: ${safeToFixed(fundDetails.aum ? fundDetails.aum / 1000000 : null, 1)} million dollars`}>
                    ${safeToFixed(fundDetails.aum ? fundDetails.aum / 1000000 : null, 1)}M
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">{t('funds.aum')}</div>
                </div>

                <div role="listitem" className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                  <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100" aria-label={`${t('funds.expenseRatio')}: ${fundDetails.expenseRatio} percent`}>
                    {safePercentage(fundDetails.expenseRatio)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">{t('funds.expenseRatio')}</div>
                </div>

                <div role="listitem" className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                  <div className="flex items-center mb-1">
                    <div
                      className="flex"
                      role="img"
                      aria-label={t('funds.fundRating', { rating: fundDetails.rating })}
                    >
                      {[...Array(5)].map((_, i) => (
                        i < fundDetails.rating ? (
                          <Star
                            key={i}
                            className="text-yellow-400 w-5 h-5"
                            aria-hidden="true"
                          />
                        ) : (
                          <StarBorder
                            key={i}
                            className="text-gray-300 dark:text-gray-600 w-5 h-5"
                            aria-hidden="true"
                          />
                        )
                      ))}
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 ml-2 font-medium" aria-hidden="true">
                      ({fundDetails.rating}/5)
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">{t('funds.rating')}</div>
                </div>
              </div>
            </Card.Content>
          </Card>

          {/* Basic Fund Information */}
          <FundBasicInfo fund={fundDetails} />

          {/* Key Performance Indicators */}
          <FundKPIs kpis={fundDetails.analytics.kpis} riskMetrics={fundDetails.analytics.riskMetrics} />
        </div>
      )
    },
    {
      id: 'performance',
      label: t('funds.performanceAnalytics'),
      children: (
        <div className="space-y-6">
          {/* Comprehensive Historical Analysis */}
          <HistoricalAnalysis fund={fundDetails} />

          {/* Real-time Market Data */}
          <MarketDataDisplay fund={fundDetails} />

          {/* Advanced Risk Analytics */}
          <RiskAnalyticsDisplay fund={fundDetails} />

          {/* Performance Comparison */}
          {fundDetails.benchmark && fundDetails.benchmark.performance && process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true' && (
            <Card>
              <Card.Header>
                <Card.Title>{t('funds.performanceVsBenchmark', { benchmarkName: fundDetails.benchmark.name })}</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(fundDetails.performance).map(([period, value]) => {
                    const benchmarkValue = fundDetails.benchmark?.performance?.[period as keyof typeof fundDetails.benchmark.performance] ?? 0;
                    const outperformance = value - benchmarkValue;
                    
                    return (
                      <div key={period} className="text-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800">
                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                          {period.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </div>
                        <div className="space-y-1">
                          <div className={`font-semibold ${value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                            {formatPercentage(value, { decimals: 2, showSign: true })}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {t('funds.benchmark')}: {formatPercentage(benchmarkValue, { decimals: 2, showSign: true })}
                          </div>
                          <div className={`text-xs font-medium ${
                            outperformance >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                          }`}>
                            {formatPercentage(outperformance, { decimals: 2, showSign: true })}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Card.Content>
            </Card>
          )}
        </div>
      )
    },
    {
      id: 'holdings',
      label: t('funds.holdingsAllocation'),
      children: (
        <div className="space-y-6">
          {/* Interactive Allocation Charts */}
          <AllocationChart
            assetAllocation={transformedHoldingsData.assetAllocation}
            geographicAllocation={transformedHoldingsData.geographicAllocation}
            sectorAllocation={transformedHoldingsData.sectorAllocation}
          />

          {/* Comprehensive Holdings Information */}
          <Card>
            <Card.Header>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Star className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                  <Card.Title>Detailed Holdings Information</Card.Title>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total Holdings: {transformedHoldingsData.topHoldings.length}
                </div>
              </div>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {/* Holdings Summary Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {transformedHoldingsData.topHoldings.length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Positions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {formatPercentage(transformedHoldingsData.topHoldings.reduce((sum: number, holding: any) => sum + holding.percentage, 0), { decimals: 1 })}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Weight</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(transformedHoldingsData.topHoldings.reduce((sum: number, holding: any) => sum + holding.marketValue, 0))}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Value</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {formatPercentage(Math.max(...transformedHoldingsData.topHoldings.map((holding: any) => holding.percentage)), { decimals: 1 })}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Largest Position</div>
                  </div>
                </div>

                {/* Enhanced Holdings List */}
                <div className="space-y-3">
                  {transformedHoldingsData.topHoldings.map((holding: any, index: number) => (
                    <div key={holding.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-blue-800 dark:text-blue-200 text-sm font-bold">
                          {index + 1}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center space-x-3 mb-1">
                            <div className="font-semibold text-gray-900 dark:text-gray-100 text-lg">{holding.name}</div>
                            <span className="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded">
                              {holding.symbol}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                            <span className="flex items-center">
                              <Business className="w-4 h-4 mr-1" />
                              {holding.sector}
                            </span>
                            <span>Market Value: {formatCurrency(holding.marketValue)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0">
                        <div className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                          {formatPercentage(holding.percentage, { decimals: 1 })}
                        </div>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(holding.percentage * 2, 100)}%` }}
                          />
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Weight
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Holdings Distribution by Sector */}
                <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <Business className="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" />
                    Sector Distribution Summary
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {Array.from(new Set(transformedHoldingsData.topHoldings.map((holding: any) => holding.sector)))
                      .map((sector: any) => {
                        const sectorHoldings = transformedHoldingsData.topHoldings.filter((holding: any) => holding.sector === sector);
                        const sectorWeight = sectorHoldings.reduce((sum: number, holding: any) => sum + holding.percentage, 0);
                        const sectorCount = sectorHoldings.length;
                        
                        return (
                          <div key={sector} className="bg-white dark:bg-gray-800 p-3 rounded border border-gray-200 dark:border-gray-600">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate" title={sector}>
                              {sector}
                            </div>
                            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                              {formatPercentage(sectorWeight, { decimals: 1 })}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              {sectorCount} position{sectorCount !== 1 ? 's' : ''}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card>
        </div>
      )
    },
    {
      id: 'news',
      label: t('funds.newsFeed'),
      children: <NewsFeed fundId={fundId} fundName={fundDetails.name} />
    },
    {
      id: 'documents',
      label: t('funds.documents'),
      children: <Documents fundId={fundId} fundName={fundDetails.name} />
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors" style={{ fontFamily: 'Lato, sans-serif' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6 sm:space-y-8">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-colors">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4 lg:gap-6">
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 truncate" id="fund-name">
                  {fundDetails.name}
                </h1>
                <span
                  className={`px-2 py-1 text-sm font-medium rounded self-start transition-colors ${
                    fundDetails.riskLevel === 'very_high' || fundDetails.riskLevel === 'high' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
                    fundDetails.riskLevel === 'moderate' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :
                    'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                  }`}
                  aria-label={`Risk level: ${fundDetails.riskLevel}`}
                >
                  {fundDetails.riskLevel.toUpperCase().replace('_', ' ')}
                </span>
              </div>
              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-gray-600 dark:text-gray-400" role="list">
                <span role="listitem">{fundDetails.type.replace('_', ' ').toUpperCase()}</span>
                <span aria-hidden="true">•</span>
                <span role="listitem">{fundDetails.category}</span>
                {fundDetails.subCategory && (
                  <>
                    <span aria-hidden="true">•</span>
                    <span role="listitem">{fundDetails.subCategory}</span>
                  </>
                )}
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={loading}
                aria-label={loading ? t('funds.refreshing') : t('funds.refreshData')}
              >
                <Refresh className="w-4 h-4 mr-2" />
                {loading ? t('funds.refreshing') : t('common.refresh')}
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = `/funds/${fundId}/market-data-input`}
                aria-label={t('funds.inputMarketData')}
              >
                <BarChart className="w-4 h-4 mr-2" />
                {t('funds.inputMarketData')}
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = `/funds/${fundId}/holdings`}
                aria-label="Manage Holdings"
              >
                <BarChart className="w-4 h-4 mr-2" />
                Manage Holdings
              </Button>
              <Button aria-label={t('funds.addToPortfolioAction')}>
                <Add className="w-4 h-4 mr-2" />
                {t('funds.addToPortfolioAction')}
              </Button>
            </div>
          </div>
        </header>

        {/* Tabbed Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-colors">
          <Tabs tabs={tabsConfig} defaultActiveTab="overview" />
        </div>
      </div>
    </div>
  );
} 