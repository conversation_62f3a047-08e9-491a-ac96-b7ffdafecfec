// Utility functions for FundFlow

/**
 * Format currency amounts with proper locale and currency symbol
 */
export const formatCurrency = (amount: number, currency = 'USD', locale = 'en-US'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Format dates with consistent formatting throughout the app
 */
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options,
  });
};

/**
 * Calculate percentage of budget spent
 */
export const calculateBudgetPercentage = (spent: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((spent / total) * 100);
};

/**
 * Generate a random ID for temporary use
 */
export const generateTempId = (): string => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Debounce function for search inputs and API calls
 */
export const debounce = <T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Convert string to URL-friendly slug
 */
export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Safe number formatting with null/undefined checks
 */
export const safeToFixed = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return value.toFixed(decimals);
};

/**
 * Safe percentage formatting (without sign)
 */
export const safePercentage = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `${value.toFixed(decimals)}%`;
};

/**
 * Safe percentage change formatting (with sign)
 */
export const safePercentageChange = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
};

/**
 * Format percentage with specified decimal places and optional sign
 */
export const formatPercentage = (
  value: number | null | undefined, 
  options?: { 
    decimals?: number, 
    showSign?: boolean, 
    fallback?: string 
  }
): string => {
  const { decimals = 1, showSign = false, fallback = 'N/A' } = options || {};
  
  if (value == null || isNaN(value)) return fallback;
  
  const sign = showSign && value >= 0 ? '+' : '';
  return `${sign}${value.toFixed(decimals)}%`;
};

/**
 * Safe currency formatting with INR symbol
 */
export const safeINR = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `₹${value.toFixed(decimals)}`;
};

/**
 * Safe currency formatting with USD symbol
 */
export const safeUSD = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `$${value.toFixed(decimals)}`;
};

/**
 * Safe currency change formatting with sign and INR symbol
 */
export const safeCurrencyChange = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `${value >= 0 ? '+' : ''}₹${value.toFixed(decimals)}`;
};

/**
 * Safe currency change formatting with sign and USD symbol
 */
export const safeCurrencyChangeUSD = (value: number | null | undefined, decimals = 2, fallback = 'N/A'): string => {
  if (value == null || isNaN(value)) return fallback;
  return `${value >= 0 ? '+' : ''}$${value.toFixed(decimals)}`;
};

/**
 * Class name utility for conditional class names
 */
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Transform backend fund holdings data to frontend analytics format
 */
export const transformHoldingsData = (fund: any) => {
  if (!fund) {
    return {
      assetAllocation: { stocks: 0, bonds: 0, cash: 0, other: 0 },
      geographicAllocation: { domestic: 0, international: 0, emerging: 0 },
      sectorAllocation: [],
      topHoldings: []
    };
  }

  // Check if mock fallback is enabled
  const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';

  // Check for holdings data in multiple locations (backend can place it in different structures)
  let holdings = [];
  
  // Priority 1: Check analytics.topHoldings (enriched data from backend)
  if (fund.analytics?.topHoldings && Array.isArray(fund.analytics.topHoldings)) {
    holdings = fund.analytics.topHoldings;
  }
  // Priority 2: Check fund.holdings.topHoldings (raw DynamoDB structure)
  else if (fund.holdings?.topHoldings && Array.isArray(fund.holdings.topHoldings)) {
    holdings = fund.holdings.topHoldings;
  }
  // Priority 3: Check if holdings is directly an array (legacy structure)
  else if (Array.isArray(fund.holdings)) {
    holdings = fund.holdings;
  }

  // Extract sector allocation data - prefer analytics first
  let sectors = [];
  if (fund.analytics?.sectorAllocation && Array.isArray(fund.analytics.sectorAllocation)) {
    sectors = fund.analytics.sectorAllocation;
  } else if (fund.sectors && Array.isArray(fund.sectors)) {
    sectors = fund.sectors;
  }
  
  // Try to extract actual asset allocation data from the backend
  let assetAllocation = { stocks: 0, bonds: 0, cash: 0, other: 0 };
  
  if (fund.analytics?.assetAllocation) {
    // Check if this looks like mock data (has many decimal places suggesting random generation)
    const analyticsData = fund.analytics.assetAllocation;
    const hasMockLikeData = Object.values(analyticsData).some((value: any) => {
      const numValue = parseFloat(value);
      return !isNaN(numValue) && numValue > 0 && value.toString().includes('.') && value.toString().split('.')[1]?.length > 4;
    });
    
    if (ENABLE_MOCK_FALLBACK || !hasMockLikeData) {
      // Use analytics data if mock fallback is enabled OR if the data doesn't look like mock data
      assetAllocation = fund.analytics.assetAllocation;
    } else {
      console.warn('🚫 Ignoring backend analytics asset allocation that appears to be mock data (ENABLE_MOCK_FALLBACK=false)');
    }
  } else if (fund.holdings?.asset_allocation) {
    // Map from backend DynamoDB structure
    const backendAssetAllocation = fund.holdings.asset_allocation;
    assetAllocation = {
      stocks: parseFloat(backendAssetAllocation.Equity || backendAssetAllocation.equity || '0'),
      bonds: parseFloat(backendAssetAllocation.Debt || backendAssetAllocation.debt || '0'),
      cash: parseFloat(backendAssetAllocation.Cash || backendAssetAllocation.cash || '0'),
      other: parseFloat(backendAssetAllocation.Others || backendAssetAllocation.other || '0')
    };
  } else if (ENABLE_MOCK_FALLBACK) {
    // Only provide default values when mock fallback is enabled
    assetAllocation = {
      stocks: 85,
      bonds: 10,
      cash: 3,
      other: 2
    };
  }
  // If mock fallback is disabled and no data exists in DynamoDB, keep zeros

  // Try to extract geographic allocation from backend
  let geographicAllocation = { domestic: 0, international: 0, emerging: 0 };
  
  if (fund.analytics?.geographicAllocation) {
    // Check if this looks like mock data (has many decimal places suggesting random generation)
    const geoData = fund.analytics.geographicAllocation;
    const hasMockLikeData = Object.values(geoData).some((value: any) => {
      const numValue = parseFloat(value);
      return !isNaN(numValue) && numValue > 0 && value.toString().includes('.') && value.toString().split('.')[1]?.length > 4;
    });
    
    if (ENABLE_MOCK_FALLBACK || !hasMockLikeData) {
      // Use analytics data if mock fallback is enabled OR if the data doesn't look like mock data
      geographicAllocation = fund.analytics.geographicAllocation;
    } else {
      console.warn('🚫 Ignoring backend analytics geographic allocation that appears to be mock data (ENABLE_MOCK_FALLBACK=false)');
    }
  } else if (fund.holdings?.geographic_allocation) {
    const backendGeoAllocation = fund.holdings.geographic_allocation;
    geographicAllocation = {
      domestic: parseFloat(backendGeoAllocation.Domestic || backendGeoAllocation.domestic || '0'),
      international: parseFloat(backendGeoAllocation.International || backendGeoAllocation.international || '0'),
      emerging: parseFloat(backendGeoAllocation.Emerging || backendGeoAllocation.emerging || '0')
    };
  } else if (ENABLE_MOCK_FALLBACK) {
    // Only provide default values when mock fallback is enabled
    geographicAllocation = {
      domestic: 75,
      international: 20,
      emerging: 5
    };
  }
  // If mock fallback is disabled and no data exists in DynamoDB, keep zeros

  // Transform sector allocation from sectors array
  const sectorAllocation = sectors.map((sector: any) => ({
    name: sector.name || sector.sector || 'Unknown',
    percentage: parseFloat(sector.percentage || sector.allocation || '0'),
    marketValue: parseFloat(sector.marketValue || '0'),
    change: parseFloat(sector.change || '0')
  }));

  // Transform holdings array to top holdings format
  const topHoldings = holdings.map((holding: any, index: number) => ({
    id: holding.id || holding.symbol || `holding-${index}`,
    name: holding.name || '',
    symbol: holding.symbol || '',
    percentage: parseFloat(holding.percentage || '0'),
    marketValue: parseFloat(holding.marketValue || '0'),
    sector: holding.sector || 'Unknown',
    country: holding.country || 'India', // Default based on holdings
    currency: holding.currency || 'INR'
  }));

  return {
    assetAllocation,
    geographicAllocation, 
    sectorAllocation,
    topHoldings
  };
}; 