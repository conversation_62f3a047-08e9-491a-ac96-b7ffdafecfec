import { Fund, ApiResponse, PaginatedResponse, FundDetails, FundPerformanceChart, TimePeriod, ChartDataPoint, Portfolio, PortfolioHolding, PortfolioTransaction, PortfolioPerformance, PortfolioCreateRequest, PortfolioUpdateRequest, AddHoldingRequest, PortfolioType, PortfolioStatus, TransactionType } from '@/types';
import { UploadResult } from '@/components/ui/UploadStatus';
import { getSession } from 'next-auth/react';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
const AWS_REGION = process.env.NEXT_PUBLIC_AWS_REGION || 'ap-northeast-1';

// Consolidated Mock Mode Configuration
// Set NEXT_PUBLIC_ENABLE_MOCK_MODE=true to enable complete mock mode (login, API fallback, and data)
// Set NEXT_PUBLIC_ENABLE_MOCK_MODE=false to use real AWS services
const ENABLE_MOCK_MODE = process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE === 'true';

// Legacy support for existing flags (will be deprecated)
const LEGACY_USE_AWS_API = process.env.NEXT_PUBLIC_USE_AWS_API === 'true';
const LEGACY_ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';

// Determine actual behavior based on new flag or legacy flags
const USE_AWS_API = ENABLE_MOCK_MODE ? false : (LEGACY_USE_AWS_API !== false);
const ENABLE_MOCK_FALLBACK = ENABLE_MOCK_MODE ? true : LEGACY_ENABLE_MOCK_FALLBACK;

console.log('🔧 API Configuration:', {
  API_BASE_URL,
  AWS_REGION,
  ENABLE_MOCK_MODE,
  USE_AWS_API,
  ENABLE_MOCK_FALLBACK,
  // Legacy flags (for debugging)
  LEGACY_USE_AWS_API,
  LEGACY_ENABLE_MOCK_FALLBACK
});

// Generic API request handler with AWS integration
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  console.log('🌐 Making API request to:', url);
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    },
    // Add mode for CORS handling
    mode: 'cors',
    credentials: 'omit', // Don't send cookies for CORS requests
  };

  // Add authentication headers if available
  if (USE_AWS_API) {
    try {
      const session = await getSession();
      console.log('🔐 Session check:', session ? 'Session found' : 'No session');
      console.log('🔐 Session details:', {
        hasAccessToken: !!session?.accessToken,
        expiresAt: session?.expiresAt,
        provider: session?.provider,
        isExpired: session?.expiresAt ? new Date(session.expiresAt * 1000) < new Date() : false
      });

      if (session?.accessToken) {
        // Check if token is expired
        if (session.expiresAt && new Date(session.expiresAt * 1000) < new Date()) {
          console.warn('⚠️ Access token has expired, API will return 401 Unauthorized');
          console.warn('💡 Please refresh the page or sign in again');
        } else {
          defaultOptions.headers = {
            ...defaultOptions.headers,
            'Authorization': `Bearer ${session.accessToken}`,
          };
          console.log('✅ Added Authorization header to request');
          if (session.expiresAt) {
            console.log('🔑 Token expires at:', new Date(session.expiresAt * 1000).toLocaleString());
          } else {
            console.log('🔑 Token expiration time not available');
          }
        }
      } else {
        console.warn('⚠️ No access token found in session, API will return 401 Unauthorized');
        console.warn('💡 Sign in at https://0fca-58-176-137-190.ngrok-free.app/auth/signin to authenticate');
      }
    } catch (error) {
      console.error('❌ Failed to get session for API request:', error);
    }
  }
  
  console.log('📋 Request headers:', defaultOptions.headers);
  console.log('🔍 Environment check:', {
    NEXT_PUBLIC_ENABLE_MOCK_MODE: process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE,
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    // Legacy flags
    NEXT_PUBLIC_USE_AWS_API: process.env.NEXT_PUBLIC_USE_AWS_API,
    NEXT_PUBLIC_ENABLE_MOCK_FALLBACK: process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK,
  });
  console.log('🚀 About to call fetch...');
  
  let response: Response;
  try {
    console.log('🚀 Making fetch request with options:', {
      url,
      method: options.method || 'GET',
      headers: defaultOptions.headers,
      mode: defaultOptions.mode,
      credentials: defaultOptions.credentials,
    });

    response = await fetch(url, {
      ...defaultOptions,
      ...options,
    });
    console.log('✅ Fetch completed successfully');
    console.log('📡 Response status:', response.status, response.statusText);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
  } catch (fetchError) {
    console.error('❌ Fetch failed with error:', fetchError);
    console.error('❌ Error type:', typeof fetchError);
    console.error('❌ Error constructor:', fetchError?.constructor?.name);

    // Log detailed error information
    console.error('Error details:', {
      name: fetchError instanceof Error ? fetchError.name : 'Unknown',
      message: fetchError instanceof Error ? fetchError.message : 'Unknown error',
      stack: fetchError instanceof Error ? fetchError.stack : 'No stack trace',
      cause: fetchError instanceof Error ? (fetchError as any).cause : undefined,
    });

    // Check for common network errors
    if (fetchError instanceof TypeError) {
      if (fetchError.message.includes('Failed to fetch')) {
        console.error('🌐 This is a "Failed to fetch" error, which could be due to:');
        console.error('  1. CORS policy blocking the request');
        console.error('  2. Network connectivity issues');
        console.error('  3. SSL/TLS certificate issues');
        console.error('  4. Server being unavailable');
        console.error('  5. Browser blocking the request');

        // Try to provide more specific guidance
        console.error('🔍 Debugging steps:');
        console.error('  1. Check browser Network tab for the actual error');
        console.error('  2. Check if the API endpoint is accessible via curl');
        console.error('  3. Verify CORS headers on the server');
        console.error('  4. Check if authentication headers are being sent correctly');

        throw new Error(`Network error: Unable to reach API at ${url}. This is likely a CORS, network, or authentication issue. Check browser console for details.`);
      }
    }

    // Re-throw the original error for other types
    throw fetchError;
  }

  if (!response.ok) {
    const errorText = await response.text();
    console.error('❌ API request failed:', response.status, response.statusText, errorText);
    
    // Handle specific HTTP error codes
    if (response.status === 401) {
      throw new Error(`Authentication failed: The API requires valid authentication. Please sign in to access fund data. (${response.status} ${response.statusText})`);
    } else if (response.status === 403) {
      throw new Error(`Access forbidden: You don't have permission to access this resource. (${response.status} ${response.statusText})`);
    } else if (response.status === 500) {
      throw new Error(`Server error: The API server encountered an internal error. Please try again later. (${response.status} ${response.statusText})`);
    } else {
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
  }

  const jsonResponse = await response.json();
  console.log('📦 API response data:', jsonResponse);
  return jsonResponse;
}

// Helper function to convert backend Fund to frontend Fund format
function convertBackendFundToFrontend(backendFund: any): Fund {
  const nav = parseFloat(backendFund.nav || '0');
  const previousNav = nav * 0.99; // Estimate previous NAV (1% down)
  const change = nav - previousNav;
  const changePercent = previousNav > 0 ? (change / previousNav) * 100 : 0;

  return {
    id: backendFund.fund_id || backendFund.id,
    name: backendFund.name || 'Unnamed Fund',
    symbol: backendFund.bloomberg_ticker || backendFund.symbol || backendFund.fund_id || 'N/A',
    type: ENABLE_MOCK_MODE ? mapFundType(backendFund.fund_type) : (backendFund.fund_type || 'equity') as Fund['type'],
    category: backendFund.custom_fields?.category || 'Mixed',
    subCategory: backendFund.custom_fields?.sub_category,
    nav: nav,
    previousNav: previousNav,
    change: change,
    changePercent: changePercent,
    volume: parseInt(backendFund.custom_fields?.volume || '0'),
    aum: parseFloat(backendFund.total_assets || '0'),
    expenseRatio: parseFloat(backendFund.expense_ratio || '0'),
    minimumInvestment: parseFloat(backendFund.minimum_investment || '0'),
    riskLevel: (backendFund.risk_level || 'moderate').toLowerCase() as Fund['riskLevel'],
    rating: parseInt(backendFund.custom_fields?.rating || '0'),
    inceptionDate: backendFund.inception_date || '2020-01-01',
    fundManager: backendFund.fund_manager || 'Unknown',
    description: backendFund.description || backendFund.investment_objective || 'Professional fund management.',
    performance: {
      oneDay: parseFloat(backendFund.performance_metrics?.one_day_return || '0'),
      oneWeek: parseFloat(backendFund.performance_metrics?.one_week_return || '0'),
      oneMonth: parseFloat(backendFund.performance_metrics?.one_month_return || '0'),
      threeMonths: parseFloat(backendFund.performance_metrics?.three_month_return || '0'),
      sixMonths: parseFloat(backendFund.performance_metrics?.six_month_return || '0'),
      oneYear: parseFloat(backendFund.performance_metrics?.one_year_return || '0'),
      threeYears: parseFloat(backendFund.performance_metrics?.three_year_return || '0'),
      fiveYears: parseFloat(backendFund.performance_metrics?.five_year_return || '0'),
    },
    holdings: Array.isArray(backendFund.holdings?.top_holdings) 
      ? {
          topHoldings: backendFund.holdings.top_holdings.map((holding: any) => ({
            id: holding.symbol || Math.random().toString(36).substring(2, 11),
            name: holding.name || 'Unknown',
            symbol: holding.symbol || 'N/A',
            percentage: parseFloat(holding.percentage || '0')
          }))
        }
      : { topHoldings: [] },
    sectors: backendFund.holdings?.sector_allocation 
      ? Object.entries(backendFund.holdings.sector_allocation).map(([name, percentage]) => ({
          name,
          percentage: parseFloat(percentage as string),
        }))
      : [],
    createdAt: backendFund.created_at || new Date().toISOString(),
    updatedAt: backendFund.updated_at || new Date().toISOString(),
  };
}

// Helper function to convert enriched backend fund data to frontend FundDetails format
function convertBackendFundDetailsToFrontend(backendFundDetails: any): FundDetails {
  // Start with the basic fund conversion
  const basicFund = convertBackendFundToFrontend(backendFundDetails);
  
  // Check if mock fallback is enabled
  const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';
  
  // Filter out backend analytics that appear to be mock data when mock fallback is disabled
  let analyticsData = backendFundDetails.analytics;
  if (analyticsData && !ENABLE_MOCK_FALLBACK) {
    // Check if analytics data looks like mock data (contains values with many decimal places)
    const checkForMockData = (obj: any): boolean => {
      return Object.values(obj || {}).some((value: any) => {
        if (typeof value === 'object' && value !== null) {
          return checkForMockData(value);
        }
        const numValue = parseFloat(value);
        return !isNaN(numValue) && numValue > 0 && value.toString().includes('.') && value.toString().split('.')[1]?.length > 4;
      });
    };
    
    if (checkForMockData(analyticsData)) {
      console.warn('🚫 Ignoring backend analytics data that appears to be mock-generated (ENABLE_MOCK_FALLBACK=false)');
      analyticsData = null;
    }
  }

  // Add the enriched analytics data that comes from the backend
  const fundDetails: FundDetails = {
    ...basicFund,
    analytics: analyticsData || {
      kpis: {
        totalReturn: 0,
        annualizedReturn: 0,
        volatility: 0,
        sharpeRatio: 0,
        sortinoRatio: 0,
        calmarRatio: 0,
        informationRatio: 0,
        treynorRatio: 0,
        alpha: 0,
        beta: 1,
        maxDrawdown: 0,
        trackingError: 0,
      },
      riskMetrics: {
        standardDeviation: 0,
        downSideRisk: 0,
        downsideDeviation: 0,
        varRisk: 0,
        var1d95: 0,
        var1d99: 0,
        cvar1d95: 0,
        cvar1d99: 0,
        sortRatio: 0,
        calmarRatio: 0,
        correlation: 0,
      },
      valuationMetrics: {
        priceToBook: 0,
        priceToEarnings: 0,
        priceToSales: 0,
        priceToCashFlow: 0,
        enterpriseValue: 0,
        evToRevenue: 0,
        evToEbitda: 0,
        returnOnEquity: 0,
        returnOnAssets: 0,
        debtToEquity: 0,
        dividendYield: 0,
        bookValuePerShare: 0,
      },
      technicalIndicators: {
        sma20: 0,
        sma50: 0,
        sma200: 0,
        rsi14: 50,
        macdLine: 0,
        macdSignal: 0,
        bollingerUpper: 0,
        bollingerLower: 0,
        vwap: 0,
        supportLevel: 0,
        resistanceLevel: 0,
      },
      assetAllocation: {
        stocks: 0,
        bonds: 0,
        cash: 0,
        other: 0,
      },
      geographicAllocation: {
        domestic: 0,
        international: 0,
        emerging: 0,
      },
      marketCapAllocation: {
        largeCap: 0,
        midCap: 0,
        smallCap: 0,
      },
      currencyAllocation: {},
      topHoldings: [],
      sectorAllocation: [],
    },
    historicalData: backendFundDetails.historicalData || [],
    currentPriceData: backendFundDetails.currentPriceData || {
      fundId: basicFund.id,
      asOf: new Date().toISOString(),
    },
    marketDataSummary: backendFundDetails.marketDataSummary || {
      lastUpdated: new Date(),
      dataSources: {},
      overallQuality: 'unknown' as const,
    },
    primaryBenchmark: backendFundDetails.primaryBenchmark || {
      benchmarkId: 'default',
      name: 'Default Benchmark',
      symbol: 'DEFAULT',
      asOf: new Date().toISOString(),
      currentValue: 100,
    },
    secondaryBenchmarks: backendFundDetails.secondaryBenchmarks || [],
    benchmark: backendFundDetails.benchmark || {
      name: 'NIFTY 50',
      symbol: 'NIFTY50',
      performance: {
        oneDay: 0,
        oneWeek: 0,
        oneMonth: 0,
        threeMonths: 0,
        sixMonths: 0,
        oneYear: 0,
        threeYears: 0,
        fiveYears: 0,
      },
    },
    documents: backendFundDetails.documents || [],
  };
  
  return fundDetails;
}

// Helper function to map backend fund types to frontend types
function mapFundType(backendType: string): Fund['type'] {
  const normalizedType = (backendType || 'equity').toLowerCase();
  const typeMap: Record<string, Fund['type']> = {
    'equity': 'mutual_fund',
    'bond': 'bond_fund',
    'mixed': 'mutual_fund',
    'money_market': 'money_market',
    'alternative': 'mutual_fund',
    'index': 'index_fund',
    'etf': 'etf',
  };
  return typeMap[normalizedType] || 'mutual_fund';
}

// Helper function to map backend risk levels to frontend risk levels
function mapRiskLevel(backendRiskLevel: string): Fund['riskLevel'] {
  const normalizedRisk = (backendRiskLevel || 'moderate').toLowerCase();
  const riskMap: Record<string, Fund['riskLevel']> = {
    'very_low': 'very_low',
    'low': 'low',
    'moderate': 'moderate',
    'medium': 'moderate',
    'high': 'high',
    'very_high': 'very_high',
  };
  return riskMap[normalizedRisk] || 'moderate';
}

// Helper function to convert frontend Fund to backend Fund format
function convertFrontendFundToBackend(frontendFund: Partial<Fund>, isCreating: boolean = false): any {
  const backendData: any = {};

  // For new fund creation, generate a unique fund_id if not provided
  if (isCreating && !frontendFund.id) {
    // Generate a unique fund ID based on name and timestamp
    const timestamp = Date.now();
    const namePrefix = frontendFund.name
      ? frontendFund.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 10).toUpperCase()
      : 'FUND';
    backendData.fund_id = `${namePrefix}-${timestamp}`;
  } else if (frontendFund.id) {
    backendData.fund_id = frontendFund.id;
  }
  if (frontendFund.name) backendData.name = frontendFund.name;
  if (frontendFund.type) backendData.fund_type = frontendFund.type === 'mutual_fund' ? 'equity' : frontendFund.type;
  if (frontendFund.nav) backendData.nav = frontendFund.nav.toString();
  if (frontendFund.aum) backendData.total_assets = frontendFund.aum.toString();
  if (frontendFund.expenseRatio) backendData.expense_ratio = frontendFund.expenseRatio.toString();
  if (frontendFund.minimumInvestment) backendData.minimum_investment = frontendFund.minimumInvestment.toString();
  if (frontendFund.riskLevel) {
    const riskMap: Record<Fund['riskLevel'], string> = {
      'very_low': 'very_low',
      'low': 'low',
      'moderate': 'moderate',
      'high': 'high',
      'very_high': 'very_high',
    };
    backendData.risk_level = riskMap[frontendFund.riskLevel];
  }
  if (frontendFund.fundManager) backendData.fund_manager = frontendFund.fundManager;
  if (frontendFund.description) backendData.description = frontendFund.description;
  if (frontendFund.symbol) backendData.bloomberg_ticker = frontendFund.symbol;
  
  // Add custom fields for frontend-specific data
  backendData.custom_fields = {
    category: frontendFund.category,
    sub_category: frontendFund.subCategory,
    volume: frontendFund.volume?.toString(),
    rating: frontendFund.rating?.toString(),
  };
  
  return backendData;
}

// Mock fund data for development/fallback
const generateMockFunds = (): Fund[] => {
  const fundTypes: Fund['type'][] = ['mutual_fund', 'etf', 'index_fund', 'bond_fund', 'money_market'];
  const riskLevels: Fund['riskLevel'][] = ['very_low', 'low', 'moderate', 'high', 'very_high'];
  const categories = ['Equity', 'Debt', 'Hybrid', 'International', 'Sectoral', 'Tax Saving'];
  const fundManagers = ['Aditya Birla', 'HDFC', 'ICICI Prudential', 'SBI', 'Axis', 'Kotak'];

  return Array.from({ length: 50 }, (_, index) => {
    const nav = 15 + Math.random() * 500;
    const previousNav = nav - (Math.random() - 0.5) * 10;
    const change = nav - previousNav;
    const changePercent = (change / previousNav) * 100;

    return {
      id: `fund-${index + 1}`,
      name: `${fundManagers[index % fundManagers.length]} ${categories[index % categories.length]} Fund ${index + 1}`,
      symbol: `${categories[index % categories.length].toUpperCase().substring(0, 3)}${(index + 1).toString().padStart(3, '0')}`,
      type: fundTypes[index % fundTypes.length],
      category: categories[index % categories.length],
      subCategory: index % 3 === 0 ? 'Large Cap' : index % 3 === 1 ? 'Mid Cap' : 'Small Cap',
      nav: Number(nav.toFixed(2)),
      previousNav: Number(previousNav.toFixed(2)),
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2)),
      volume: Math.floor(Math.random() * 1000000) + 10000,
      aum: Math.floor(Math.random() * 10000) + 500, // in crores
      expenseRatio: Number((0.5 + Math.random() * 2).toFixed(2)),
      minimumInvestment: [500, 1000, 5000, 10000][index % 4],
      riskLevel: riskLevels[index % riskLevels.length],
      rating: Math.floor(Math.random() * 5) + 1,
      inceptionDate: new Date(2020 - Math.floor(Math.random() * 10), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString(),
      fundManager: fundManagers[index % fundManagers.length],
      description: `A professionally managed investment fund that pools money from many investors to purchase securities. This ${categories[index % categories.length].toLowerCase()} fund focuses on long-term capital appreciation.`,
      performance: {
        oneDay: Number((Math.random() - 0.5).toFixed(2)),
        oneWeek: Number((Math.random() * 4 - 2).toFixed(2)),
        oneMonth: Number((Math.random() * 8 - 4).toFixed(2)),
        threeMonths: Number((Math.random() * 15 - 7.5).toFixed(2)),
        sixMonths: Number((Math.random() * 20 - 10).toFixed(2)),
        oneYear: Number((Math.random() * 30 - 15).toFixed(2)),
        threeYears: Number((Math.random() * 40 - 10).toFixed(2)),
        fiveYears: Number((Math.random() * 60 - 5).toFixed(2)),
      },
      holdings: {
        topHoldings: [
          { id: '1', name: 'Reliance Industries', symbol: 'RELIANCE', percentage: 8.5 },
          { id: '2', name: 'HDFC Bank', symbol: 'HDFCBANK', percentage: 7.2 },
          { id: '3', name: 'Infosys', symbol: 'INFY', percentage: 6.8 },
        ],
      },
      sectors: [
        { name: 'Financial Services', percentage: 25.4 },
        { name: 'Information Technology', percentage: 18.7 },
        { name: 'Consumer Goods', percentage: 12.3 },
      ],
      createdAt: new Date(2023, 0, 1).toISOString(),
      updatedAt: new Date().toISOString(),
    };
  });
};

// Generate mock detailed fund data
const generateMockFundDetails = (fundId: string): FundDetails => {
  const baseFund = generateMockFunds().find(f => f.id === fundId);
  if (!baseFund) {
    throw new Error('Fund not found');
  }

  return {
    ...baseFund,
    analytics: {
      kpis: {
        totalReturn: Number((Math.random() * 200 - 50).toFixed(2)),
        annualizedReturn: Number((Math.random() * 30 - 5).toFixed(2)),
        volatility: Number((Math.random() * 25 + 5).toFixed(2)),
        sharpeRatio: Number((Math.random() * 2 - 0.5).toFixed(2)),
        sortinoRatio: Number((Math.random() * 2.5 - 0.5).toFixed(2)),
        calmarRatio: Number((Math.random() * 2).toFixed(2)),
        informationRatio: Number((Math.random() * 2 - 0.5).toFixed(2)),
        treynorRatio: Number((Math.random() * 0.15 - 0.05).toFixed(3)),
        alpha: Number((Math.random() * 10 - 5).toFixed(2)),
        beta: Number((Math.random() * 0.5 + 0.8).toFixed(2)),
        maxDrawdown: Number((Math.random() * -30).toFixed(2)),
        trackingError: Number((Math.random() * 5 + 1).toFixed(2)),
      },
      riskMetrics: {
        standardDeviation: Number((Math.random() * 20 + 5).toFixed(2)),
        downSideRisk: Number((Math.random() * 15 + 3).toFixed(2)),
        downsideDeviation: Number((Math.random() * 12 + 2).toFixed(2)),
        varRisk: Number((Math.random() * -20).toFixed(2)),
        var1d95: Number((Math.random() * -5).toFixed(2)),
        var1d99: Number((Math.random() * -8).toFixed(2)),
        cvar1d95: Number((Math.random() * -7).toFixed(2)),
        cvar1d99: Number((Math.random() * -12).toFixed(2)),
        sortRatio: Number((Math.random() * 1.5).toFixed(2)),
        calmarRatio: Number((Math.random() * 2).toFixed(2)),
        correlation: Number((Math.random() * 0.4 + 0.6).toFixed(2)),
      },
      valuationMetrics: {
        priceToBook: Number((Math.random() * 3 + 0.5).toFixed(2)),
        priceToEarnings: Number((Math.random() * 25 + 5).toFixed(2)),
        priceToSales: Number((Math.random() * 5 + 0.5).toFixed(2)),
        priceToCashFlow: Number((Math.random() * 15 + 3).toFixed(2)),
        enterpriseValue: Number((Math.random() * 50000 + 10000).toFixed(0)),
        evToRevenue: Number((Math.random() * 8 + 1).toFixed(2)),
        evToEbitda: Number((Math.random() * 20 + 5).toFixed(2)),
        returnOnEquity: Number((Math.random() * 25 + 5).toFixed(2)),
        returnOnAssets: Number((Math.random() * 15 + 2).toFixed(2)),
        debtToEquity: Number((Math.random() * 1.5 + 0.1).toFixed(2)),
        dividendYield: Number((Math.random() * 5 + 0.5).toFixed(2)),
        bookValuePerShare: Number((Math.random() * 50 + 10).toFixed(2)),
      },
      technicalIndicators: {
        sma20: Number((baseFund.nav * (1 + (Math.random() - 0.5) * 0.1)).toFixed(2)),
        sma50: Number((baseFund.nav * (1 + (Math.random() - 0.5) * 0.15)).toFixed(2)),
        sma200: Number((baseFund.nav * (1 + (Math.random() - 0.5) * 0.25)).toFixed(2)),
        rsi14: Number((Math.random() * 60 + 20).toFixed(2)),
        macdLine: Number((Math.random() * 2 - 1).toFixed(3)),
        macdSignal: Number((Math.random() * 2 - 1).toFixed(3)),
        bollingerUpper: Number((baseFund.nav * 1.05).toFixed(2)),
        bollingerLower: Number((baseFund.nav * 0.95).toFixed(2)),
        vwap: Number((baseFund.nav * (1 + (Math.random() - 0.5) * 0.02)).toFixed(2)),
        supportLevel: Number((baseFund.nav * 0.92).toFixed(2)),
        resistanceLevel: Number((baseFund.nav * 1.08).toFixed(2)),
      },
      assetAllocation: {
        stocks: Number((Math.random() * 80 + 10).toFixed(1)),
        bonds: Number((Math.random() * 30 + 5).toFixed(1)),
        cash: Number((Math.random() * 10 + 2).toFixed(1)),
        other: Number((Math.random() * 5).toFixed(1)),
      },
      geographicAllocation: {
        domestic: Number((Math.random() * 70 + 20).toFixed(1)),
        international: Number((Math.random() * 30 + 5).toFixed(1)),
        emerging: Number((Math.random() * 15).toFixed(1)),
      },
      marketCapAllocation: {
        largeCap: Number((Math.random() * 60 + 30).toFixed(1)),
        midCap: Number((Math.random() * 30 + 10).toFixed(1)),
        smallCap: Number((Math.random() * 20 + 5).toFixed(1)),
      },
      currencyAllocation: {
        USD: Number((Math.random() * 70 + 20).toFixed(1)),
        EUR: Number((Math.random() * 20 + 5).toFixed(1)),
        GBP: Number((Math.random() * 15 + 2).toFixed(1)),
        JPY: Number((Math.random() * 10 + 1).toFixed(1)),
        Other: Number((Math.random() * 5).toFixed(1)),
      },
      topHoldings: [
        {
          id: '1',
          name: 'Reliance Industries',
          symbol: 'RELIANCE',
          percentage: 8.5,
          marketValue: *********,
          sector: 'Oil & Gas',
        },
        {
          id: '2',
          name: 'HDFC Bank',
          symbol: 'HDFCBANK',
          percentage: 7.2,
          marketValue: *********,
          sector: 'Banking',
        },
        {
          id: '3',
          name: 'Infosys',
          symbol: 'INFY',
          percentage: 6.8,
          marketValue: *********,
          sector: 'Information Technology',
        },
        {
          id: '4',
          name: 'ICICI Bank',
          symbol: 'ICICIBANK',
          percentage: 5.9,
          marketValue: *********,
          sector: 'Banking',
        },
        {
          id: '5',
          name: 'TCS',
          symbol: 'TCS',
          percentage: 5.4,
          marketValue: *********,
          sector: 'Information Technology',
        },
      ],
      sectorAllocation: [
        {
          name: 'Financial Services',
          percentage: 25.4,
          marketValue: **********,
          change: 1.2,
        },
        {
          name: 'Information Technology',
          percentage: 18.7,
          marketValue: **********,
          change: -0.8,
        },
        {
          name: 'Consumer Goods',
          percentage: 12.3,
          marketValue: **********,
          change: 0.5,
        },
        {
          name: 'Healthcare',
          percentage: 8.9,
          marketValue: *********,
          change: 2.1,
        },
        {
          name: 'Energy',
          percentage: 7.8,
          marketValue: *********,
          change: -1.3,
        },
      ],
    },
    historicalData: generateMockHistoricalData('1Y'),

    // Enhanced market data
    currentPriceData: {
      fundId: fundId,
      asOf: new Date().toISOString(),
      nav: {
        timestamp: new Date().toISOString(),
        value: baseFund.nav,
        source: 'fund_company' as const,
        quality: 'good' as const,
        currency: 'USD',
      },
      marketPrice: {
        timestamp: new Date().toISOString(),
        value: baseFund.nav * (1 + (Math.random() - 0.5) * 0.02),
        source: 'yahoo_finance' as const,
        quality: 'good' as const,
        currency: 'USD',
      },
      volume: baseFund.volume,
      avgVolume30d: Math.floor(baseFund.volume * (0.8 + Math.random() * 0.4)),
      bidAskSpread: Number((baseFund.nav * 0.001).toFixed(3)),
      bidAskSpreadPct: Number((0.1 + Math.random() * 0.1).toFixed(3)),
      marketCap: baseFund.aum * 1000000, // Convert crores to actual value
      sharesOutstanding: Math.floor(baseFund.aum * 1000000 / baseFund.nav),
      priceChange1d: baseFund.change,
      priceChange1dPct: baseFund.changePercent,
      priceChangeYtd: Number((Math.random() * 30 - 10).toFixed(2)),
      priceChangeYtdPct: Number((Math.random() * 25 - 5).toFixed(2)),
    },

    marketDataSummary: {
      lastUpdated: new Date().toISOString(),
      dataSources: {
        nav: 'fund_company' as const,
        marketPrice: 'yahoo_finance' as const,
        volume: 'yahoo_finance' as const,
        technicalIndicators: 'bloomberg' as const,
      },
      overallQuality: 'good' as const,
    },

    primaryBenchmark: {
      benchmarkId: 'nifty50',
      name: 'NIFTY 50',
      symbol: 'NIFTY50',
      asOf: new Date().toISOString(),
      currentValue: 19500 + Math.random() * 1000,
      return1d: Number((Math.random() - 0.5).toFixed(2)),
      return1w: Number((Math.random() * 4 - 2).toFixed(2)),
      return1m: Number((Math.random() * 8 - 4).toFixed(2)),
      return3m: Number((Math.random() * 15 - 7.5).toFixed(2)),
      return6m: Number((Math.random() * 20 - 10).toFixed(2)),
      return1y: Number((Math.random() * 30 - 15).toFixed(2)),
      return3y: Number((Math.random() * 40 - 10).toFixed(2)),
      return5y: Number((Math.random() * 60 - 5).toFixed(2)),
      volatility: Number((Math.random() * 20 + 10).toFixed(2)),
      maxDrawdown: Number((Math.random() * -25).toFixed(2)),
    },

    secondaryBenchmarks: [
      {
        benchmarkId: 'sensex',
        name: 'BSE SENSEX',
        symbol: 'SENSEX',
        asOf: new Date().toISOString(),
        currentValue: 65000 + Math.random() * 3000,
        return1d: Number((Math.random() - 0.5).toFixed(2)),
        return1w: Number((Math.random() * 4 - 2).toFixed(2)),
        return1m: Number((Math.random() * 8 - 4).toFixed(2)),
        return3m: Number((Math.random() * 15 - 7.5).toFixed(2)),
        return6m: Number((Math.random() * 20 - 10).toFixed(2)),
        return1y: Number((Math.random() * 30 - 15).toFixed(2)),
        return3y: Number((Math.random() * 40 - 10).toFixed(2)),
        return5y: Number((Math.random() * 60 - 5).toFixed(2)),
        volatility: Number((Math.random() * 20 + 10).toFixed(2)),
        maxDrawdown: Number((Math.random() * -25).toFixed(2)),
      },
    ],

    benchmark: {
      name: 'NIFTY 50',
      symbol: 'NIFTY50',
      performance: {
        oneDay: Number((Math.random() - 0.5).toFixed(2)),
        oneWeek: Number((Math.random() * 4 - 2).toFixed(2)),
        oneMonth: Number((Math.random() * 8 - 4).toFixed(2)),
        threeMonths: Number((Math.random() * 15 - 7.5).toFixed(2)),
        sixMonths: Number((Math.random() * 20 - 10).toFixed(2)),
        oneYear: Number((Math.random() * 30 - 15).toFixed(2)),
        threeYears: Number((Math.random() * 40 - 10).toFixed(2)),
        fiveYears: Number((Math.random() * 60 - 5).toFixed(2)),
      },
    },
    documents: [
      {
        id: '1',
        name: 'Fund Factsheet',
        type: 'factsheet',
        url: '/documents/factsheet.pdf',
        uploadDate: '2024-01-01T00:00:00.000Z',
      },
      {
        id: '2',
        name: 'Annual Report 2023',
        type: 'annual_report',
        url: '/documents/annual_report_2023.pdf',
        uploadDate: '2024-03-15T00:00:00.000Z',
      },
    ],
  };
};

// Generate mock historical data for charts
const generateMockHistoricalData = (period: TimePeriod): ChartDataPoint[] => {
  const dataPoints: ChartDataPoint[] = [];
  const now = new Date();
  let startDate: Date;
  let dayInterval = 1;

  switch (period) {
    case '1D':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      dayInterval = 1/24; // Hourly data
      break;
    case '1W':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      dayInterval = 1/4; // 6-hour intervals
      break;
    case '1M':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      dayInterval = 1; // Daily
      break;
    case '3M':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      dayInterval = 1; // Daily
      break;
    case '6M':
      startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
      dayInterval = 1; // Daily
      break;
    case '1Y':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      dayInterval = 1; // Daily
      break;
    case '3Y':
      startDate = new Date(now.getTime() - 3 * 365 * 24 * 60 * 60 * 1000);
      dayInterval = 7; // Weekly
      break;
    case '5Y':
      startDate = new Date(now.getTime() - 5 * 365 * 24 * 60 * 60 * 1000);
      dayInterval = 7; // Weekly
      break;
    default:
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      dayInterval = 1;
  }

  let currentDate = new Date(startDate);
  let baseValue = 100;

  while (currentDate <= now) {
    const dailyChange = (Math.random() - 0.5) * 4; // -2% to +2% daily change
    baseValue += dailyChange;
    
    dataPoints.push({
      date: currentDate.toISOString().split('T')[0],
      value: Number(baseValue.toFixed(2)),
      nav: Number(baseValue.toFixed(2)), // NAV same as value for funds
      volume: Math.floor(Math.random() * 1000000) + 10000,
      returns: Number(dailyChange.toFixed(2)),
    });

    currentDate = new Date(currentDate.getTime() + dayInterval * 24 * 60 * 60 * 1000);
  }

  return dataPoints;
};

// Generate mock market data for testing
const generateMockMarketData = (fundId: string) => {
  return {
    fund_id: fundId,
    last_updated: new Date().toISOString(),
    price_data: {
      fund_id: fundId,
      as_of: new Date().toISOString(),
      nav: {
        timestamp: new Date().toISOString(),
        value: "125.45",
        source: "fund_company",
        quality: "excellent",
        currency: "USD"
      },
      market_price: {
        timestamp: new Date().toISOString(),
        value: "125.50",
        source: "yahoo_finance",
        quality: "good",
        currency: "USD"
      },
      volume: 150000,
      bid_ask_spread: "0.05",
      market_cap: "5000000000"
    },
    valuation_metrics: {
      fund_id: fundId,
      as_of: new Date().toISOString().split('T')[0],
      price_to_book: "2.45",
      price_to_earnings: "18.5",
      dividend_yield: "2.8",
      return_on_equity: "15.2"
    },
    technical_indicators: {
      fund_id: fundId,
      as_of: new Date().toISOString(),
      sma_20: "124.80",
      sma_50: "123.45",
      rsi_14: "65.4",
      support_level: "121.00",
      resistance_level: "128.00"
    },
    risk_analytics: {
      fund_id: fundId,
      as_of: new Date().toISOString().split('T')[0],
      var_1d_95: "-2.5",
      sharpe_ratio: "1.25",
      max_drawdown: "-15.2",
      volatility: "18.5",
      beta: "1.05"
    },
    data_sources: {
      nav: "fund_company",
      market_price: "yahoo_finance",
      technical_indicators: "bloomberg"
    },
    overall_quality: "good"
  };
};

// Parse market data functions
const parseValuationMetrics = (data: any) => {
  if (!data) return {};
  return {
    priceToBook: parseFloat(data.price_to_book) || 0,
    priceToEarnings: parseFloat(data.price_to_earnings) || 0,
    priceToSales: parseFloat(data.price_to_sales) || 0,
    priceToCashFlow: parseFloat(data.price_to_cash_flow) || 0,
    enterpriseValue: parseFloat(data.enterprise_value) || 0,
    evToRevenue: parseFloat(data.ev_to_revenue) || 0,
    evToEbitda: parseFloat(data.ev_to_ebitda) || 0,
    returnOnEquity: parseFloat(data.return_on_equity) || 0,
    returnOnAssets: parseFloat(data.return_on_assets) || 0,
    debtToEquity: parseFloat(data.debt_to_equity) || 0,
    dividendYield: parseFloat(data.dividend_yield) || 0,
    bookValuePerShare: parseFloat(data.book_value_per_share) || 0,
  };
};

const parseTechnicalIndicators = (data: any) => {
  if (!data) return {};
  return {
    sma20: parseFloat(data.sma_20) || 0,
    sma50: parseFloat(data.sma_50) || 0,
    sma200: parseFloat(data.sma_200) || 0,
    rsi14: parseFloat(data.rsi_14) || 50,
    macdLine: parseFloat(data.macd_line) || 0,
    macdSignal: parseFloat(data.macd_signal) || 0,
    bollingerUpper: parseFloat(data.bollinger_upper) || 0,
    bollingerLower: parseFloat(data.bollinger_lower) || 0,
    vwap: parseFloat(data.vwap) || 0,
    supportLevel: parseFloat(data.support_level) || 0,
    resistanceLevel: parseFloat(data.resistance_level) || 0,
  };
};

const parseRiskAnalytics = (data: any) => {
  if (!data) return {};
  return {
    var1d95: parseFloat(data.var_1d_95) || 0,
    var1d99: parseFloat(data.var_1d_99) || 0,
    cvar1d95: parseFloat(data.cvar_1d_95) || 0,
    cvar1d99: parseFloat(data.cvar_1d_99) || 0,
    sharpeRatio: parseFloat(data.sharpe_ratio) || 0,
    sortinoRatio: parseFloat(data.sortino_ratio) || 0,
    calmarRatio: parseFloat(data.calmar_ratio) || 0,
    maxDrawdown: parseFloat(data.max_drawdown) || 0,
    volatility: parseFloat(data.volatility) || 0,
    beta: parseFloat(data.beta) || 1,
    correlation: parseFloat(data.correlation) || 0,
  };
};

// Mock portfolio data for development/fallback
const generateMockPortfolios = (): Portfolio[] => {
  const portfolioTypes: PortfolioType[] = ['personal', 'retirement', 'taxable', 'trust', 'corporate', 'education'];
  const portfolioStatuses: PortfolioStatus[] = ['active', 'inactive', 'closed'];
  const riskLevels = ['very_low', 'low', 'moderate', 'high', 'very_high'] as const;
  const portfolioNames = [
    'Growth Portfolio', 'Conservative Portfolio', 'Balanced Portfolio', 'Aggressive Growth',
    'Income Portfolio', 'Retirement Fund', 'Emergency Fund', 'Education Savings',
    'Tax-Advantaged Portfolio', 'International Portfolio'
  ];

  return Array.from({ length: 10 }, (_, index) => {
    const totalValue = 50000 + Math.random() * 500000;
    const totalCostBasis = totalValue * (0.8 + Math.random() * 0.3); // 80-110% of current value
    const totalGainLoss = totalValue - totalCostBasis;
    const totalGainLossPct = (totalGainLoss / totalCostBasis) * 100;
    const cashBalance = totalValue * (0.02 + Math.random() * 0.08); // 2-10% cash

    // Generate holdings
    const numHoldings = 3 + Math.floor(Math.random() * 8); // 3-10 holdings
    const mockFunds = generateMockFunds();
    const selectedFunds = mockFunds.slice(0, numHoldings);

    const holdings: PortfolioHolding[] = selectedFunds.map((fund) => {
      const weight = Math.random() * 30 + 5; // 5-35% weight
      const marketValue = (totalValue - cashBalance) * (weight / 100);
      const shares = marketValue / fund.nav;
      const averageCost = fund.nav * (0.9 + Math.random() * 0.2); // ±10% from current price
      const costBasis = shares * averageCost;
      const unrealizedGainLoss = marketValue - costBasis;
      const unrealizedGainLossPct = (unrealizedGainLoss / costBasis) * 100;

      return {
        fundId: fund.id,
        fundName: fund.name,
        fundSymbol: fund.symbol,
        shares: Number(shares.toFixed(4)),
        averageCost: Number(averageCost.toFixed(2)),
        currentPrice: fund.nav,
        marketValue: Number(marketValue.toFixed(2)),
        costBasis: Number(costBasis.toFixed(2)),
        unrealizedGainLoss: Number(unrealizedGainLoss.toFixed(2)),
        unrealizedGainLossPct: Number(unrealizedGainLossPct.toFixed(2)),
        weight: Number(weight.toFixed(2)),
        firstPurchaseDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 2).toISOString(),
        lastUpdated: new Date().toISOString(),
      };
    });

    // Generate recent transactions
    const numTransactions = 5 + Math.floor(Math.random() * 10); // 5-15 transactions
    const transactionTypes: TransactionType[] = ['buy', 'sell', 'dividend', 'interest', 'fee'];

    const recentTransactions: PortfolioTransaction[] = Array.from({ length: Math.min(numTransactions, 10) }, (_, txIndex) => {
      const fund = selectedFunds[Math.floor(Math.random() * selectedFunds.length)];
      const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const shares = transactionType === 'buy' || transactionType === 'sell' ? Math.random() * 100 + 10 : undefined;
      const price = transactionType === 'buy' || transactionType === 'sell' ? fund.nav * (0.95 + Math.random() * 0.1) : undefined;
      const amount = shares && price ? shares * price : Math.random() * 1000 + 100;
      const fees = transactionType === 'buy' || transactionType === 'sell' ? Math.random() * 20 + 5 : 0;

      return {
        transactionId: `tx-${index}-${txIndex}`,
        fundId: fund.id,
        fundName: fund.name,
        fundSymbol: fund.symbol,
        transactionType,
        transactionDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
        shares: shares ? Number(shares.toFixed(4)) : undefined,
        price: price ? Number(price.toFixed(2)) : undefined,
        amount: Number(amount.toFixed(2)),
        fees: Number(fees.toFixed(2)),
        netAmount: Number((amount - fees).toFixed(2)),
        description: `${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)} transaction for ${fund.name}`,
        referenceNumber: `REF${Date.now()}${txIndex}`,
        createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      };
    });

    // Generate performance metrics
    const performance: PortfolioPerformance = {
      totalReturn: Number(totalGainLoss.toFixed(2)),
      totalReturnPct: Number(totalGainLossPct.toFixed(2)),
      oneDayReturn: Number((Math.random() * 4 - 2).toFixed(2)),
      oneWeekReturn: Number((Math.random() * 8 - 4).toFixed(2)),
      oneMonthReturn: Number((Math.random() * 12 - 6).toFixed(2)),
      threeMonthReturn: Number((Math.random() * 20 - 10).toFixed(2)),
      sixMonthReturn: Number((Math.random() * 30 - 15).toFixed(2)),
      oneYearReturn: Number((Math.random() * 40 - 20).toFixed(2)),
      threeYearReturn: Number((Math.random() * 60 - 30).toFixed(2)),
      fiveYearReturn: Number((Math.random() * 80 - 40).toFixed(2)),
      inceptionReturn: Number(totalGainLossPct.toFixed(2)),
      volatility: Number((Math.random() * 20 + 5).toFixed(2)),
      sharpeRatio: Number((Math.random() * 2 - 0.5).toFixed(2)),
      maxDrawdown: Number((Math.random() * -15 - 5).toFixed(2)),
      benchmarkReturn: Number((Math.random() * 30 - 15).toFixed(2)),
      alpha: Number((Math.random() * 10 - 5).toFixed(2)),
      beta: Number((Math.random() * 0.5 + 0.8).toFixed(2)),
      asOfDate: new Date().toISOString(),
    };

    return {
      portfolioId: `portfolio-${index + 1}`,
      name: `${portfolioNames[index % portfolioNames.length]} ${index + 1}`,
      description: `A ${portfolioTypes[index % portfolioTypes.length]} portfolio designed for ${riskLevels[index % riskLevels.length]} risk tolerance with diversified holdings.`,
      portfolioType: portfolioTypes[index % portfolioTypes.length],
      status: portfolioStatuses[index % portfolioStatuses.length],
      userId: `user-${Math.floor(index / 2) + 1}`, // Multiple portfolios per user
      baseCurrency: 'USD',
      inceptionDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 3).toISOString(),
      totalValue: Number(totalValue.toFixed(2)),
      totalCostBasis: Number(totalCostBasis.toFixed(2)),
      cashBalance: Number(cashBalance.toFixed(2)),
      totalGainLoss: Number(totalGainLoss.toFixed(2)),
      totalGainLossPct: Number(totalGainLossPct.toFixed(2)),
      holdings,
      recentTransactions,
      performance,
      riskLevel: riskLevels[index % riskLevels.length],
      benchmark: index % 3 === 0 ? 'S&P 500' : index % 3 === 1 ? 'NASDAQ' : 'Russell 2000',
      tags: [`${portfolioTypes[index % portfolioTypes.length]}`, `${riskLevels[index % riskLevels.length]}-risk`],
      customFields: {
        advisor: `Advisor ${Math.floor(index / 3) + 1}`,
        strategy: `Strategy ${index % 4 + 1}`,
        rebalanceFrequency: index % 2 === 0 ? 'quarterly' : 'semi-annually',
      },
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 3).toISOString(),
      updatedAt: new Date().toISOString(),
      lastRebalanced: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString() : undefined,
      holdingsCount: holdings.length,
      transactionsCount: numTransactions,
    };
  });
};

// Fund API functions
export const fundApi = {
  // Get all funds
  getFunds: async (pageSize: number = 100): Promise<ApiResponse<Fund[]>> => {
    console.log('🔍 getFunds called', {
      ENABLE_MOCK_MODE,
      USE_AWS_API,
      ENABLE_MOCK_FALLBACK,
      API_BASE_URL
    });
    
    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock data');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: generateMockFunds(),
            message: 'Funds fetched successfully (mock data - AWS API disabled)',
            success: true,
          });
        }, 1000);
      });
    }

    try {
      console.log('🚀 Making API request to /funds...');

      // Try direct API call first
      let response;
      try {
        console.log('🔄 Attempting direct API call to /funds...');
        response = await apiRequest<{
          success: boolean;
          message: string;
          data: {
            funds: any[];
          };
        }>(`/funds?page_size=${pageSize}`);
      } catch (directApiError) {
        console.warn('❌ Direct API call failed, trying proxy endpoint...', directApiError);

        // If direct API fails (likely CORS), try the proxy endpoint
        try {
          console.log('🔄 Attempting proxy API call...');
          const proxyResponse = await fetch(`/api/proxy/funds?page_size=${pageSize}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          });

          if (!proxyResponse.ok) {
            const errorText = await proxyResponse.text();

            // Check if it's a token expiration error
            if (proxyResponse.status === 401) {
              try {
                const errorData = JSON.parse(errorText);
                if (errorData.requiresSignIn) {
                  console.warn('🔄 Token expired, redirecting to sign in...');
                  // Redirect to sign in page
                  window.location.href = '/auth/signin?callbackUrl=' + encodeURIComponent(window.location.href);
                  throw new Error('Authentication required - redirecting to sign in');
                }
              } catch (e) {
                // If parsing fails, continue with normal error handling
              }
            }

            throw new Error(`Proxy API failed: ${proxyResponse.status} ${proxyResponse.statusText} - ${errorText}`);
          }

          response = await proxyResponse.json();
          console.log('✅ Proxy API response received:', response);
        } catch (proxyError) {
          console.error('❌ Proxy API also failed:', proxyError);
          throw directApiError; // Throw the original error
        }
      }

      console.log('✅ API response received:', response);
      console.log('📊 Response success flag:', response.success);
      console.log('📊 Response message:', response.message);
      console.log('📊 Full API response structure:', JSON.stringify(response, null, 2));
      console.log('📊 Response.data type:', typeof response.data);
      console.log('📊 Response.data.funds type:', typeof response.data?.funds);
      console.log('📊 Response.data.funds length:', response.data?.funds?.length);
      
      // The backend response structure is: { data: { funds: [...] } }
      // We need to extract the funds array and create our expected format
      if (!response.data || !response.data.funds || !Array.isArray(response.data.funds)) {
        throw new Error('Invalid API response structure: expected data.funds to be an array');
      }
      
      console.log('📊 Raw funds data sample:', response.data.funds[0]);
      
      const funds = response.data.funds.map((fund: any, index: number) => {
        try {
          console.log(`🔄 Converting fund ${index}:`, fund.name || fund.fund_id);
          const converted = convertBackendFundToFrontend(fund);
          console.log(`✅ Converted fund ${index}:`, converted.name, converted.nav);
          return converted;
        } catch (error) {
          console.error(`❌ Failed to convert fund at index ${index}:`, fund, error);
          throw new Error(`Failed to convert fund "${fund.name || fund.fund_id}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      console.log('✅ Successfully converted', funds.length, 'funds');
      console.log('📊 Sample converted fund:', funds[0]);
      
      // Return the expected format for the frontend
      return {
        data: funds,
        message: `Retrieved ${funds.length} funds successfully`,
        success: true, // Since we got here without throwing, it's successful
      };
    } catch (error) {
      console.error('❌ All API attempts failed:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');

        // Fallback to mock data when enabled
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: generateMockFunds(),
              message: `Using mock data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
            });
          }, 500);
        });
      } else {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
    }
  },

  // Get paginated funds
  getFundsPaginated: async (
    page: number = 1,
    limit: number = 20,
    filters?: Partial<{
      search: string;
      type: string;
      category: string;
      riskLevel: string;
    }>
  ): Promise<PaginatedResponse<Fund>> => {
    console.log('🔍 getFundsPaginated called, USE_AWS_API:', USE_AWS_API);
    
    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock data');
      return new Promise((resolve) => {
        setTimeout(() => {
          const allFunds = generateMockFunds();
          const startIndex = (page - 1) * limit;
          const endIndex = startIndex + limit;
          const paginatedFunds = allFunds.slice(startIndex, endIndex);

          resolve({
            data: paginatedFunds,
            message: 'Funds fetched successfully (mock data - AWS API disabled)',
            success: true,
            pagination: {
              page,
              limit,
              total: allFunds.length,
              totalPages: Math.ceil(allFunds.length / limit),
            },
          });
        }, 800);
      });
    }

    try {
      const queryParams = new URLSearchParams({
        page_size: limit.toString(),
        ...(filters?.search && { search: filters.search }),
        ...(filters?.type && { fund_type: filters.type }),
      });

      console.log('🚀 Making paginated API request to /funds with params:', queryParams.toString());
      const response = await apiRequest<{
        success: boolean;
        message: string;
        data: {
          funds: any[];
          pagination: {
            page: number;
            page_size: number;
            total_count: number;
            has_more: boolean;
          };
        };
      }>(`/funds?${queryParams}`);

      console.log('✅ AWS API paginated response received:', response);
      const funds = response.data.funds.map(convertBackendFundToFrontend);
      
      return {
        data: funds,
        message: response.message || 'Funds fetched successfully',
        success: response.success,
        pagination: {
          page: response.data.pagination.page,
          limit: response.data.pagination.page_size,
          total: response.data.pagination.total_count,
          totalPages: Math.ceil(response.data.pagination.total_count / response.data.pagination.page_size),
        },
      };
    } catch (error) {
      console.error('❌ AWS API paginated request failed:', error);
      
      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
        
        // Fallback to mock data when enabled
        return new Promise((resolve) => {
          setTimeout(() => {
            const allFunds = generateMockFunds();
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedFunds = allFunds.slice(startIndex, endIndex);

            resolve({
              data: paginatedFunds,
              message: `Using mock data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
              pagination: {
                page,
                limit,
                total: allFunds.length,
                totalPages: Math.ceil(allFunds.length / limit),
              },
            });
          }, 500);
        });
      } else {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
    }
  },

  // Get fund by ID
  getFundById: async (id: string): Promise<ApiResponse<Fund>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success?: boolean;
          message?: string;
          data: any;
          timestamp?: string;
        }>(`/funds/${id}`);

        // Check if we have data (backend may not include success field)
        if (response.data) {
          const fund = convertBackendFundToFrontend(response.data);

          return {
            data: fund,
            message: response.message || 'Fund fetched successfully',
            success: true, // Set success to true if we have data
          };
        } else {
          throw new Error(response.message || 'No fund data received from API');
        }
      }
    } catch (error) {
      console.error('❌ AWS API failed for getFundById:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }

      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data (when AWS API disabled or when enabled and AWS API fails)
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const funds = generateMockFunds();
        const fund = funds.find(f => f.id === id);
        
        if (fund) {
          resolve({
            data: fund,
            message: 'Fund fetched successfully (mock data)',
            success: true,
          });
        } else {
          reject(new Error('Fund not found'));
        }
      }, 500);
    });
  },

  // Search funds
  searchFunds: async (query: string): Promise<ApiResponse<Fund[]>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: {
            funds: any[];
          };
        }>(`/funds?search=${encodeURIComponent(query)}`);

        const funds = response.data.funds.map(convertBackendFundToFrontend);
        
        return {
          data: funds,
          message: response.message || 'Search completed successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for searchFunds:', error);
      
      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
      
      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        const funds = generateMockFunds();
        const filteredFunds = funds.filter(
          fund =>
            fund.name.toLowerCase().includes(query.toLowerCase()) ||
            fund.symbol.toLowerCase().includes(query.toLowerCase()) ||
            fund.category.toLowerCase().includes(query.toLowerCase())
        );

        resolve({
          data: filteredFunds,
          message: 'Search completed successfully (mock data)',
          success: true,
        });
      }, 600);
    });
  },

  // Get enhanced market data for a fund
  getFundMarketData: async (fundId: string): Promise<ApiResponse<any>> => {
    console.log('🔍 getFundMarketData called for fund:', fundId);

    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock market data');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            data: generateMockMarketData(fundId),
            message: 'Mock market data retrieved successfully',
          });
        }, 500);
      });
    }

    try {
      const response = await apiRequest<{
        success: boolean;
        data: any;
        message?: string;
      }>(`/funds/${fundId}/market-data`);

      console.log('✅ Market data response received:', response);

      return {
        success: true,
        data: response.data,
        message: response.message || 'Market data retrieved successfully',
      };
    } catch (error) {
      console.error('Error fetching market data:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.log('🔄 Falling back to mock market data');
        return {
          success: true,
          data: generateMockMarketData(fundId),
          message: 'Market data retrieved successfully (fallback)',
        };
      }

      // When mock fallback is disabled, return failure instead of mock data
      console.error('🚫 Mock market data fallback disabled (ENABLE_MOCK_FALLBACK=false). Returning failure.');
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : 'Failed to fetch market data',
      };
    }
  },

  // Submit market data input
  submitMarketDataInput: async (fundId: string, marketData: any): Promise<ApiResponse<any>> => {
    console.log('🔍 submitMarketDataInput called for fund:', fundId);

    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock submission');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            data: {
              input_id: `mock_input_${Date.now()}`,
              fund_id: fundId,
              validated: marketData.validated || false,
            },
            message: 'Market data input submitted successfully (mock)',
          });
        }, 1000);
      });
    }

    try {
      const response = await apiRequest<{
        success: boolean;
        data: any;
        message?: string;
      }>(`/funds/${fundId}/market-data`, {
        method: 'POST',
        body: JSON.stringify(marketData),
      });

      console.log('✅ Market data submission response received:', response);

      return {
        success: true,
        data: response.data,
        message: response.message || 'Market data submitted successfully',
      };
    } catch (error) {
      console.error('Error submitting market data:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : 'Failed to submit market data',
      };
    }
  },

  // Get detailed fund information
  getFundDetails: async (id: string): Promise<ApiResponse<FundDetails>> => {
    try {
      if (USE_AWS_API) {
        // Use the enhanced /funds/{id}/details endpoint for enriched analytics
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>(`/funds/${id}/details`);

        // Convert the backend data to frontend format
        let fundDetails = convertBackendFundDetailsToFrontend(response.data);

        // Try to fetch enhanced market data
        try {
          console.log('🔍 Fetching enhanced market data for fund:', id);
          const marketDataResponse = await fundApi.getFundMarketData(id);

          if (marketDataResponse.success && marketDataResponse.data) {
            console.log('✅ Enhanced market data retrieved:', marketDataResponse.data);

            // Integrate market data into fund details
            fundDetails = {
              ...fundDetails,
              currentPriceData: marketDataResponse.data.price_data,
              marketDataSummary: {
                lastUpdated: marketDataResponse.data.last_updated,
                dataSources: marketDataResponse.data.data_sources || {},
                overallQuality: marketDataResponse.data.overall_quality || 'unknown',
              },
              analytics: {
                ...fundDetails.analytics,
                valuationMetrics: {
                  ...fundDetails.analytics.valuationMetrics,
                  ...parseValuationMetrics(marketDataResponse.data.valuation_metrics),
                },
                technicalIndicators: {
                  ...fundDetails.analytics.technicalIndicators,
                  ...parseTechnicalIndicators(marketDataResponse.data.technical_indicators),
                },
                riskMetrics: {
                  ...fundDetails.analytics.riskMetrics,
                  ...parseRiskAnalytics(marketDataResponse.data.risk_analytics),
                },
              },
            };

            console.log('✅ Enhanced fund details with market data:', fundDetails);
          } else {
            console.log('⚠️ Could not fetch enhanced market data, using basic fund details');
          }
        } catch (marketDataError) {
          console.warn('⚠️ Failed to fetch enhanced market data:', marketDataError);
          // Continue with basic fund details
        }

        return {
          data: fundDetails,
          message: response.message || 'Fund details fetched successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for getFundDetails:', error);
      
      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
      
      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const fundDetails = generateMockFundDetails(id);
          resolve({
            data: fundDetails,
            message: 'Fund details fetched successfully (mock data)',
            success: true,
          });
        } catch (error) {
          reject(new Error('Fund not found'));
        }
      }, 800);
    });
  },

  // Get fund performance chart data
  getFundPerformanceChart: async (
    id: string,
    period: TimePeriod,
    includeBenchmark: boolean = true
  ): Promise<ApiResponse<FundPerformanceChart>> => {
    // For development, return mock data
    if (process.env.NODE_ENV === 'development') {
      return new Promise((resolve) => {
        setTimeout(() => {
          const data = generateMockHistoricalData(period);
          const benchmarkData = includeBenchmark ? generateMockHistoricalData(period) : undefined;

          resolve({
            data: {
              timePeriod: period,
              data,
              benchmarkData,
            },
            message: 'Performance chart data fetched successfully',
            success: true,
          });
        }, 600);
      });
    }

    // Production API call
    const params = new URLSearchParams({
      period,
      includeBenchmark: includeBenchmark.toString(),
    });
    return apiRequest<ApiResponse<FundPerformanceChart>>(`/funds/${id}/performance?${params}`);
  },

  // Get fund historical data for specific period
  getFundHistoricalData: async (
    id: string,
    period: TimePeriod
  ): Promise<ApiResponse<ChartDataPoint[]>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: {
            timePeriod: string;
            data: ChartDataPoint[];
            benchmarkData?: ChartDataPoint[];
          };
        }>(`/funds/${id}/historical?period=${period}`);

        return {
          data: response.data.data,
          message: response.message || 'Historical data fetched successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for getFundHistoricalData:', error);
      
      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
      
      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        const data = generateMockHistoricalData(period);
        resolve({
          data,
          message: 'Historical data fetched successfully (mock data)',
          success: true,
        });
      }, 500);
    });
  },

  // Update fund
  updateFund: async (id: string, fundData: Partial<Fund>): Promise<ApiResponse<FundDetails>> => {
    try {
      if (USE_AWS_API) {
        // Convert frontend fund data to backend format
        const backendFundData = convertFrontendFundToBackend(fundData);
        
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>(`/funds/${id}`, {
          method: 'PUT',
          body: JSON.stringify(backendFundData),
        });

        const fundDetails = generateMockFundDetails(id); // Use mock details for additional data
        const fund = convertBackendFundToFrontend(response.data);
        
        // Merge AWS data with mock detailed data
        const mergedDetails = {
          ...fundDetails,
          ...fund,
          updatedAt: new Date().toISOString(),
        };
        
        return {
          data: mergedDetails,
          message: response.message || 'Fund updated successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for updateFund:', error);
      
      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
      
      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Get existing fund details
          const existingFund = generateMockFundDetails(id);
          
          // Merge with updates
          const updatedFund = {
            ...existingFund,
            ...fundData,
            updatedAt: new Date().toISOString(),
          };

          resolve({
            data: updatedFund,
            message: 'Fund updated successfully (mock data)',
            success: true,
          });
        } catch (error) {
          reject(new Error('Fund not found'));
        }
      }, 800);
    });
  },

  // Create new fund
  createFund: async (fundData: Partial<Fund>): Promise<ApiResponse<FundDetails>> => {
    try {
      if (USE_AWS_API) {
        // Convert frontend fund data to backend format
        const backendFundData = convertFrontendFundToBackend(fundData, true);
        
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>('/funds', {
          method: 'POST',
          body: JSON.stringify(backendFundData),
        });

        // Backend returns { data: { fund: { ... } } }
        const responseFundData = response.data.fund || response.data;
        const fund = convertBackendFundToFrontend(responseFundData);

        // For newly created funds, create fund details directly instead of using mock data
        // since the fund won't exist in the mock data yet
        const fundDetails: FundDetails = {
          ...fund,
          analytics: {
            kpis: {
              totalReturn: 0,
              annualizedReturn: 0,
              volatility: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              informationRatio: 0,
              treynorRatio: 0,
              alpha: 0,
              beta: 1,
              maxDrawdown: 0,
              trackingError: 0,
            },
            riskMetrics: {
              standardDeviation: 0,
              downSideRisk: 0,
              downsideDeviation: 0,
              varRisk: 0,
              var1d95: 0,
              var1d99: 0,
              cvar1d95: 0,
              cvar1d99: 0,
              sortRatio: 0,
              calmarRatio: 0,
              correlation: 0,
            },
            valuationMetrics: {
              priceToBook: 0,
              priceToEarnings: 0,
              priceToSales: 0,
              priceToCashFlow: 0,
              enterpriseValue: 0,
              evToRevenue: 0,
              evToEbitda: 0,
              returnOnEquity: 0,
              returnOnAssets: 0,
              debtToEquity: 0,
              dividendYield: 0,
              bookValuePerShare: 0,
            },
            technicalIndicators: {
              sma20: 0,
              sma50: 0,
              sma200: 0,
              rsi14: 50,
              macdLine: 0,
              macdSignal: 0,
              bollingerUpper: 0,
              bollingerLower: 0,
              vwap: 0,
              supportLevel: 0,
              resistanceLevel: 0,
            },
            assetAllocation: {
              stocks: 0,
              bonds: 0,
              cash: 0,
              other: 0,
            },
            geographicAllocation: {
              domestic: 100,
              international: 0,
              emerging: 0,
            },
            marketCapAllocation: {
              largeCap: 0,
              midCap: 0,
              smallCap: 0,
            },
            currencyAllocation: {
              USD: 100,
            },
            topHoldings: fund.holdings?.topHoldings?.map((holding: any) => ({
              ...holding,
              marketValue: 0,
              sector: 'Unknown',
              country: 'Unknown',
              currency: 'USD',
            })) || [],
            sectorAllocation: fund.sectors?.map(sector => ({
              ...sector,
              marketValue: 0,
              change: 0,
            })) || [],
          },
          historicalData: [],
          currentPriceData: {
            fundId: fund.id,
            asOf: new Date().toISOString(),
            nav: {
              timestamp: new Date().toISOString(),
              value: fund.nav,
              source: 'fund_company' as const,
              quality: 'good' as const,
              currency: 'USD',
            },
            volume: fund.volume,
            priceChange1d: fund.change,
            priceChange1dPct: fund.changePercent,
          },
          marketDataSummary: {
            lastUpdated: new Date().toISOString(),
            dataSources: {},
            overallQuality: 'good' as const,
          },
          primaryBenchmark: {
            benchmarkId: 'nifty50',
            name: 'NIFTY 50',
            symbol: 'NIFTY50',
            asOf: new Date().toISOString(),
            currentValue: 19500,
          },
          secondaryBenchmarks: [],
          benchmark: {
            name: 'NIFTY 50',
            symbol: 'NIFTY50',
            performance: {
              oneDay: 0,
              oneWeek: 0,
              oneMonth: 0,
              threeMonths: 0,
              sixMonths: 0,
              oneYear: 0,
              threeYears: 0,
              fiveYears: 0,
            },
          },
          documents: [],
        };

        const mergedDetails = fundDetails;
        
        return {
          data: mergedDetails,
          message: response.message || 'Fund created successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for createFund:', error);
      
      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
      
      console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        const newFund = {
          ...generateMockFundDetails('new-fund'),
          ...fundData,
          id: `fund-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        resolve({
          data: newFund,
          message: 'Fund created successfully (mock data)',
          success: true,
        });
      }, 1000);
    });
  },

  // Process PDF files synchronously
  submitPDFJobs: async (files: File[]): Promise<ApiResponse<{jobId: string, status: string, fileName: string, result?: any}[]>> => {
    console.log('🔍 submitPDFJobs called', {
      fileCount: files.length,
      ENABLE_MOCK_MODE,
      USE_AWS_API,
      ENABLE_MOCK_FALLBACK,
    });

    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock data');
      return new Promise((resolve) => {
        setTimeout(() => {
          const jobs = files.map((file, index) => ({
            jobId: `mock-job-${Date.now()}-${index}`,
            status: 'processing',
            fileName: file.name
          }));
          resolve({
            data: jobs,
            message: `Processed ${files.length} PDF files successfully (mock data - AWS API disabled)`,
            success: true,
          });
        }, 1000);
      });
    }

    try {
      console.log('🚀 Processing PDF files synchronously...');

      // Process files synchronously
      const jobSubmissions: {jobId: string, status: string, fileName: string, result?: any}[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          console.log(`📄 Processing file ${i + 1}/${files.length}: ${file.name}`);

          // Create FormData for single file upload
          const formData = new FormData();
          formData.append('file', file);

          // Get authentication session
          const session = await getSession();
          if (!session?.accessToken) {
            throw new Error('Authentication required. Please sign in to upload PDF files.');
          }

          // Submit file to the PDF extraction endpoint (synchronous processing)
          const submitResponse = await fetch(`${API_BASE_URL}/funds/extract-pdf?save=true`, {
            method: 'POST',
            body: formData,
            headers: {
              'Authorization': `Bearer ${session.accessToken}`,
              'Accept': 'application/json',
              // Note: File name is already included in FormData, no need for custom header
            },
            mode: 'cors',
            credentials: 'omit',
          });

          if (!submitResponse.ok) {
            const errorText = await submitResponse.text();
            console.error(`❌ Failed to process ${file.name}:`, errorText);
            throw new Error(`Failed to process ${file.name}: ${errorText}`);
          }

          const submitResult = await submitResponse.json();
          console.log(`✅ Successfully processed ${file.name}`);

          // Since this is synchronous processing, we get the result immediately
          jobSubmissions.push({
            jobId: `sync-${Date.now()}-${i}`, // Generate a fake job ID for compatibility
            status: 'completed', // Mark as completed since processing is synchronous
            fileName: file.name,
            result: submitResult.data // Store the extracted fund data
          });

        } catch (error) {
          console.error(`❌ Failed to process ${file.name}:`, error);
          throw error;
        }
      }

      console.log(`✅ Successfully processed ${jobSubmissions.length} PDF files`);

      return {
        data: jobSubmissions,
        message: `Successfully submitted ${jobSubmissions.length} PDF processing jobs`,
        success: true,
      };

    } catch (error) {
      console.error('❌ Error submitting PDF jobs:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');

        // Fallback to mock data when enabled
        return new Promise((resolve) => {
          setTimeout(() => {
            const jobs = files.map((file, index) => ({
              jobId: `mock-fallback-job-${Date.now()}-${index}`,
              status: 'processing',
              fileName: file.name
            }));
            resolve({
              data: jobs,
              message: `Using mock data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
            });
          }, 1000);
        });
      }

      throw error;
    }
  },

  // Check status of PDF processing jobs
  checkPDFJobStatus: async (jobId: string): Promise<ApiResponse<any>> => {
    console.log('🔍 checkPDFJobStatus called for job:', jobId);

    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock status');
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockStatuses = ['processing', 'completed', 'failed'];
          const status = mockStatuses[Math.floor(Math.random() * mockStatuses.length)];
          resolve({
            data: {
              jobId,
              status,
              ...(status === 'completed' && {
                result: {
                  id: `mock-fund-${Date.now()}`,
                  name: 'Mock Extracted Fund',
                  symbol: 'MOCK',
                  saved_to_database: true
                }
              }),
              ...(status === 'failed' && {
                error: 'Mock processing error'
              })
            },
            message: `Job status retrieved successfully (mock data)`,
            success: true,
          });
        }, 500);
      });
    }

    try {
      // Get authentication session
      const session = await getSession();
      if (!session?.accessToken) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_BASE_URL}/pdf-jobs/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
          'Accept': 'application/json',
        },
        mode: 'cors',
        credentials: 'omit',
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get job status: ${errorText}`);
      }

      const result = await response.json();
      return {
        data: result.data,
        message: result.message,
        success: true,
      };

    } catch (error) {
      console.error('❌ Error checking job status:', error);
      throw error;
    }
  },

  // Generate mock upload results for development/testing
  generateMockUploadResults: (files: File[]): UploadResult[] => {
    const mockFunds = generateMockFunds();

    return files.map((file, index) => {
      const processingTime = 1000 + Math.random() * 3000; // 1-4 seconds
      const isSuccess = Math.random() > 0.2; // 80% success rate
      const hasWarnings = Math.random() > 0.7; // 30% chance of warnings

      if (isSuccess) {
        // Use a mock fund for successful conversion
        const fundData = mockFunds[index % mockFunds.length];
        // Modify the fund data to make it look like it came from PDF
        const convertedFund: Fund = {
          ...fundData,
          id: `pdf-${Date.now()}-${index}`,
          name: `${file.name.replace('.pdf', '')} Fund`,
          description: `Fund data extracted from ${file.name}`,
        };

        return {
          id: `result-${Date.now()}-${index}`,
          fileName: file.name,
          status: hasWarnings ? 'warning' : 'success',
          message: hasWarnings
            ? 'Fund data extracted successfully with some warnings'
            : 'Fund data extracted successfully',
          fundData: convertedFund,
          processingTime,
          warnings: hasWarnings ? [
            'Some fields could not be extracted automatically',
            'Please verify the expense ratio manually',
          ] : undefined,
        };
      } else {
        // Generate error result
        const errorMessages = [
          'PDF format not supported - please ensure the PDF contains structured fund data',
          'Unable to extract fund information - PDF may be corrupted or password protected',
          'Text extraction failed - PDF may contain only images',
          'Fund data format not recognized - please check the PDF structure',
        ];

        return {
          id: `result-${Date.now()}-${index}`,
          fileName: file.name,
          status: 'error',
          message: 'Failed to extract fund data',
          error: errorMessages[Math.floor(Math.random() * errorMessages.length)],
          processingTime,
        };
      }
    });
  },

  // Bulk PDF upload for fund data (synchronous processing)
  bulkUploadPDFs: async (files: File[]): Promise<ApiResponse<UploadResult[]>> => {
    console.log('🔍 bulkUploadPDFs called', {
      fileCount: files.length,
      ENABLE_MOCK_MODE,
      USE_AWS_API,
      ENABLE_MOCK_FALLBACK,
    });

    try {
      // Step 1: Process all files synchronously
      console.log('📤 Step 1: Processing files synchronously...');
      const jobSubmissionResponse = await fundApi.submitPDFJobs(files);

      if (!jobSubmissionResponse.success || !jobSubmissionResponse.data) {
        throw new Error('Failed to process PDF files');
      }

      const jobs = jobSubmissionResponse.data;
      console.log(`✅ Processed ${jobs.length} files synchronously`);

      // Step 2: Convert synchronous results to upload results
      console.log('📋 Step 2: Converting results...');
      const uploadResults: UploadResult[] = [];

      for (const job of jobs) {
        if (job.status === 'completed' && job.result) {
          // Job completed successfully
          const result: UploadResult = {
            id: `result-${job.jobId}`,
            fileName: job.fileName,
            status: 'success',
            message: 'Fund data extracted successfully',
            fundData: job.result,
            processingTime: 0, // Synchronous processing
          };
          uploadResults.push(result);
          console.log(`✅ Successfully processed ${job.fileName}`);
        } else {
          // Job failed or incomplete
          const result: UploadResult = {
            id: `result-${job.jobId}`,
            fileName: job.fileName,
            status: 'error',
            message: 'Failed to extract fund data',
            error: 'Processing failed or incomplete',
            processingTime: 0,
          };
          uploadResults.push(result);
          console.log(`❌ Failed to process ${job.fileName}`);
        }
      }

      console.log(`🎉 Completed processing ${uploadResults.length} files`);

      return {
        data: uploadResults,
        message: `Processed ${uploadResults.length} PDF files`,
        success: true,
      };

    } catch (error) {
      console.error('❌ Error in bulkUploadPDFs:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');

        // Fallback to mock data when enabled
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: fundApi.generateMockUploadResults(files),
              message: `Using mock data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
            });
          }, 2000);
        });
      }

      throw error;
    }
  },

  // Legacy function - keeping for backward compatibility but now uses async processing
  bulkUploadPDFsLegacy: async (files: File[]): Promise<ApiResponse<UploadResult[]>> => {
    console.log('🔍 bulkUploadPDFsLegacy called', {
      fileCount: files.length,
      ENABLE_MOCK_MODE,
      USE_AWS_API,
      ENABLE_MOCK_FALLBACK,
    });



    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock data');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: fundApi.generateMockUploadResults(files),
            message: `Processed ${files.length} PDF files successfully (mock data - AWS API disabled)`,
            success: true,
          });
        }, 2000); // Simulate processing time
      });
    }

    try {
      console.log('🚀 Processing PDF files with real API...');

      // Process files sequentially since the Lambda function handles one file at a time
      const uploadResults: UploadResult[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const startTime = Date.now();

        try {
          console.log(`📄 Processing file ${i + 1}/${files.length}: ${file.name}`);

          // Create FormData for single file upload
          const formData = new FormData();
          formData.append('file', file);

          // Get authentication session
          const session = await getSession();
          if (!session?.accessToken) {
            throw new Error('Authentication required. Please sign in to upload PDF files.');
          }

          // Call the PDF fund extractor endpoint
          const uploadResponse = await fetch(`${API_BASE_URL}/funds/extract-pdf?save=true`, {
            method: 'POST',
            body: formData,
            headers: {
              'Authorization': `Bearer ${session.accessToken}`,
              'Accept': 'application/json',
            },
            mode: 'cors',
            credentials: 'omit',
          });

          const processingTime = Date.now() - startTime;

          if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            console.error(`❌ Failed to process ${file.name}:`, errorText);

            // Add error result
            uploadResults.push({
              id: `result-${Date.now()}-${i}`,
              fileName: file.name,
              status: 'error',
              message: 'Failed to extract fund data',
              error: `API request failed: ${uploadResponse.status} ${uploadResponse.statusText}`,
              processingTime,
            });
            continue;
          }

          const response = await uploadResponse.json();
          console.log(`✅ Successfully processed ${file.name}:`, response);

          // Convert the API response to our expected format
          if (response.success && response.data) {
            // Convert backend fund data to frontend format
            const fundData = convertBackendFundToFrontend(response.data);

            uploadResults.push({
              id: `result-${Date.now()}-${i}`,
              fileName: file.name,
              status: response.data.saved_to_database ? 'success' : 'warning',
              message: response.message || 'Fund data extracted successfully',
              fundData: fundData,
              processingTime,
              warnings: response.data.saved_to_database ? undefined : [
                'Fund data extracted but not saved to database',
                'Please review and save manually if needed'
              ],
            });
          } else {
            // Handle API success but no data
            uploadResults.push({
              id: `result-${Date.now()}-${i}`,
              fileName: file.name,
              status: 'error',
              message: 'Failed to extract fund data',
              error: response.message || 'No fund data returned from API',
              processingTime,
            });
          }
        } catch (fileError) {
          const processingTime = Date.now() - startTime;
          console.error(`❌ Error processing ${file.name}:`, fileError);

          uploadResults.push({
            id: `result-${Date.now()}-${i}`,
            fileName: file.name,
            status: 'error',
            message: 'Failed to extract fund data',
            error: fileError instanceof Error ? fileError.message : String(fileError),
            processingTime,
          });
        }
      }

      const successCount = uploadResults.filter(r => r.status === 'success').length;
      const warningCount = uploadResults.filter(r => r.status === 'warning').length;
      const errorCount = uploadResults.filter(r => r.status === 'error').length;

      return {
        data: uploadResults,
        message: `Processed ${files.length} PDF files: ${successCount} successful, ${warningCount} with warnings, ${errorCount} failed`,
        success: true,
      };
    } catch (error) {
      console.error('❌ Bulk PDF upload failed:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock data due to API failure (ENABLE_MOCK_FALLBACK=true)');

        // Fallback to mock data when enabled
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: fundApi.generateMockUploadResults(files),
              message: `Using mock data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
            });
          }, 1500);
        });
      } else {
        console.error('🚫 Mock data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error; // Re-throw the actual error when mock fallback is disabled
      }
    }
  },
};

// Helper function to convert backend portfolio data to frontend format
function convertBackendPortfolioToFrontend(backendPortfolio: any): Portfolio {
  return {
    portfolioId: backendPortfolio.portfolio_id || backendPortfolio.portfolioId,
    name: backendPortfolio.name || 'Unnamed Portfolio',
    description: backendPortfolio.description || '',
    portfolioType: backendPortfolio.portfolio_type || backendPortfolio.portfolioType || 'personal',
    status: backendPortfolio.status || 'active',
    userId: backendPortfolio.user_id || backendPortfolio.userId,
    baseCurrency: backendPortfolio.base_currency || backendPortfolio.baseCurrency || 'USD',
    inceptionDate: backendPortfolio.inception_date || backendPortfolio.inceptionDate || new Date().toISOString(),
    totalValue: parseFloat(backendPortfolio.total_value || backendPortfolio.totalValue || '0'),
    totalCostBasis: parseFloat(backendPortfolio.total_cost_basis || backendPortfolio.totalCostBasis || '0'),
    cashBalance: parseFloat(backendPortfolio.cash_balance || backendPortfolio.cashBalance || '0'),
    totalGainLoss: parseFloat(backendPortfolio.total_gain_loss || backendPortfolio.totalGainLoss || '0'),
    totalGainLossPct: parseFloat(backendPortfolio.total_gain_loss_pct || backendPortfolio.totalGainLossPct || '0'),
    holdings: Array.isArray(backendPortfolio.holdings) 
      ? backendPortfolio.holdings.map((holding: any) => ({
          fundId: holding.fund_id || holding.fundId,
          fundName: holding.fund_name || holding.fundName,
          fundSymbol: holding.fund_symbol || holding.fundSymbol,
          shares: parseFloat(holding.shares || '0'),
          averageCost: parseFloat(holding.average_cost || holding.averageCost || '0'),
          currentPrice: parseFloat(holding.current_price || holding.currentPrice || '0'),
          marketValue: parseFloat(holding.market_value || holding.marketValue || '0'),
          costBasis: parseFloat(holding.cost_basis || holding.costBasis || '0'),
          unrealizedGainLoss: parseFloat(holding.unrealized_gain_loss || holding.unrealizedGainLoss || '0'),
          unrealizedGainLossPct: parseFloat(holding.unrealized_gain_loss_pct || holding.unrealizedGainLossPct || '0'),
          weight: parseFloat(holding.weight || '0'),
          firstPurchaseDate: holding.first_purchase_date || holding.firstPurchaseDate || new Date().toISOString(),
          lastUpdated: holding.last_updated || holding.lastUpdated || new Date().toISOString(),
        }))
      : [],
    recentTransactions: Array.isArray(backendPortfolio.recent_transactions) 
      ? backendPortfolio.recent_transactions.map((tx: any) => ({
          transactionId: tx.transaction_id || tx.transactionId,
          fundId: tx.fund_id || tx.fundId,
          fundName: tx.fund_name || tx.fundName,
          fundSymbol: tx.fund_symbol || tx.fundSymbol,
          transactionType: tx.transaction_type || tx.transactionType,
          transactionDate: tx.transaction_date || tx.transactionDate,
          settlementDate: tx.settlement_date || tx.settlementDate,
          shares: tx.shares !== null ? parseFloat(tx.shares || '0') : undefined,
          price: tx.price !== null ? parseFloat(tx.price || '0') : undefined,
          amount: parseFloat(tx.amount || '0'),
          fees: parseFloat(tx.fees || '0'),
          netAmount: parseFloat(tx.net_amount || tx.netAmount || '0'),
          description: tx.description || '',
          referenceNumber: tx.reference_number || tx.referenceNumber,
          createdAt: tx.created_at || tx.createdAt || new Date().toISOString(),
        }))
      : [],
    performance: backendPortfolio.performance ? {
      totalReturn: parseFloat(backendPortfolio.performance.total_return || backendPortfolio.performance.totalReturn || '0'),
      totalReturnPct: parseFloat(backendPortfolio.performance.total_return_pct || backendPortfolio.performance.totalReturnPct || '0'),
      oneDayReturn: parseFloat(backendPortfolio.performance.one_day_return || backendPortfolio.performance.oneDayReturn || '0'),
      oneWeekReturn: parseFloat(backendPortfolio.performance.one_week_return || backendPortfolio.performance.oneWeekReturn || '0'),
      oneMonthReturn: parseFloat(backendPortfolio.performance.one_month_return || backendPortfolio.performance.oneMonthReturn || '0'),
      threeMonthReturn: parseFloat(backendPortfolio.performance.three_month_return || backendPortfolio.performance.threeMonthReturn || '0'),
      sixMonthReturn: parseFloat(backendPortfolio.performance.six_month_return || backendPortfolio.performance.sixMonthReturn || '0'),
      oneYearReturn: parseFloat(backendPortfolio.performance.one_year_return || backendPortfolio.performance.oneYearReturn || '0'),
      threeYearReturn: parseFloat(backendPortfolio.performance.three_year_return || backendPortfolio.performance.threeYearReturn || '0'),
      fiveYearReturn: parseFloat(backendPortfolio.performance.five_year_return || backendPortfolio.performance.fiveYearReturn || '0'),
      inceptionReturn: parseFloat(backendPortfolio.performance.inception_return || backendPortfolio.performance.inceptionReturn || '0'),
      volatility: parseFloat(backendPortfolio.performance.volatility || '0'),
      sharpeRatio: parseFloat(backendPortfolio.performance.sharpe_ratio || backendPortfolio.performance.sharpeRatio || '0'),
      maxDrawdown: parseFloat(backendPortfolio.performance.max_drawdown || backendPortfolio.performance.maxDrawdown || '0'),
      benchmarkReturn: parseFloat(backendPortfolio.performance.benchmark_return || backendPortfolio.performance.benchmarkReturn || '0'),
      alpha: parseFloat(backendPortfolio.performance.alpha || '0'),
      beta: parseFloat(backendPortfolio.performance.beta || '0'),
      asOfDate: backendPortfolio.performance.as_of_date || backendPortfolio.performance.asOfDate || new Date().toISOString(),
    } : {
      totalReturn: 0, totalReturnPct: 0, oneDayReturn: 0, oneWeekReturn: 0, oneMonthReturn: 0,
      threeMonthReturn: 0, sixMonthReturn: 0, oneYearReturn: 0, threeYearReturn: 0, fiveYearReturn: 0,
      inceptionReturn: 0, volatility: 0, sharpeRatio: 0, maxDrawdown: 0, benchmarkReturn: 0,
      alpha: 0, beta: 0, asOfDate: new Date().toISOString(),
    },
    riskLevel: backendPortfolio.risk_level || backendPortfolio.riskLevel || 'medium',
    benchmark: backendPortfolio.benchmark || 'S&P 500',
    tags: Array.isArray(backendPortfolio.tags) ? backendPortfolio.tags : [],
    customFields: backendPortfolio.custom_fields || backendPortfolio.customFields || {},
    createdAt: backendPortfolio.created_at || backendPortfolio.createdAt || new Date().toISOString(),
    updatedAt: backendPortfolio.updated_at || backendPortfolio.updatedAt || new Date().toISOString(),
    lastRebalanced: backendPortfolio.last_rebalanced || backendPortfolio.lastRebalanced,
  };
}

// Portfolio API functions
export const portfolioApi = {
  // Get all portfolios
  getPortfolios: async (): Promise<ApiResponse<Portfolio[]>> => {
    console.log('🔍 getPortfolios called, USE_AWS_API:', USE_AWS_API);

    if (!USE_AWS_API) {
      console.log('⚠️ AWS API disabled, using mock portfolio data');
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: generateMockPortfolios(),
            message: 'Portfolios fetched successfully (mock data - AWS API disabled)',
            success: true,
          });
        }, 1000);
      });
    }

    try {
      console.log('🚀 Making API request to /portfolios...');

      const response = await apiRequest<{
        success: boolean;
        message: string;
        data: {
          portfolios: any[];
        };
      }>('/portfolios');

      console.log('✅ Portfolios API response received:', response);

      if (response.data && response.data.portfolios) {
        return {
          data: response.data.portfolios.map(convertBackendPortfolioToFrontend),
          message: response.message || 'Portfolios fetched successfully',
          success: true,
        };
      }

      throw new Error('Invalid response format from portfolios API');
    } catch (error) {
      console.error('❌ All portfolio API attempts failed:', error);

      if (ENABLE_MOCK_FALLBACK) {
        console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');

        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: generateMockPortfolios(),
              message: `Using mock portfolio data - API unavailable: ${error instanceof Error ? error.message : String(error)}`,
              success: true,
            });
          }, 500);
        });
      } else {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }
    }
  },

  // Get portfolio by ID
  getPortfolioById: async (id: string): Promise<ApiResponse<Portfolio>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success?: boolean;
          message?: string;
          data: any;
        }>(`/portfolios/${id}`);

        if (response.data) {
          return {
            data: response.data,
            message: response.message || 'Portfolio fetched successfully',
            success: response.success !== false,
          };
        }
      }
    } catch (error) {
      console.error('❌ AWS API failed for getPortfolioById:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }

      console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const portfolios = generateMockPortfolios();
        const portfolio = portfolios.find(p => p.portfolioId === id);

        if (portfolio) {
          resolve({
            data: portfolio,
            message: 'Portfolio fetched successfully (mock data)',
            success: true,
          });
        } else {
          reject(new Error('Portfolio not found'));
        }
      }, 500);
    });
  },

  // Create new portfolio
  createPortfolio: async (portfolioData: PortfolioCreateRequest): Promise<ApiResponse<Portfolio>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>('/portfolios', {
          method: 'POST',
          body: JSON.stringify(portfolioData),
        });

        return {
          data: response.data,
          message: response.message || 'Portfolio created successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for createPortfolio:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }

      console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        const newPortfolio: Portfolio = {
          portfolioId: `portfolio-${Date.now()}`,
          name: portfolioData.name,
          description: portfolioData.description,
          portfolioType: portfolioData.portfolioType,
          status: 'active',
          userId: 'current-user',
          baseCurrency: portfolioData.baseCurrency || 'USD',
          inceptionDate: portfolioData.inceptionDate || new Date().toISOString(),
          totalValue: portfolioData.cashBalance || 0,
          totalCostBasis: portfolioData.cashBalance || 0,
          cashBalance: portfolioData.cashBalance || 0,
          totalGainLoss: 0,
          totalGainLossPct: 0,
          holdings: [],
          recentTransactions: [],
          riskLevel: portfolioData.riskLevel,
          benchmark: portfolioData.benchmark,
          tags: portfolioData.tags || [],
          customFields: portfolioData.customFields || {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          holdingsCount: 0,
          transactionsCount: 0,
        };

        resolve({
          data: newPortfolio,
          message: 'Portfolio created successfully (mock data)',
          success: true,
        });
      }, 1000);
    });
  },

  // Update portfolio
  updatePortfolio: async (id: string, portfolioData: PortfolioUpdateRequest): Promise<ApiResponse<Portfolio>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>(`/portfolios/${id}`, {
          method: 'PUT',
          body: JSON.stringify(portfolioData),
        });

        return {
          data: response.data,
          message: response.message || 'Portfolio updated successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for updatePortfolio:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }

      console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const portfolios = generateMockPortfolios();
          const existingPortfolio = portfolios.find(p => p.portfolioId === id);

          if (!existingPortfolio) {
            reject(new Error('Portfolio not found'));
            return;
          }

          const updatedPortfolio = {
            ...existingPortfolio,
            ...portfolioData,
            updatedAt: new Date().toISOString(),
          };

          resolve({
            data: updatedPortfolio,
            message: 'Portfolio updated successfully (mock data)',
            success: true,
          });
        } catch (error) {
          reject(new Error('Portfolio not found'));
        }
      }, 800);
    });
  },

  // Delete portfolio
  deletePortfolio: async (id: string): Promise<ApiResponse<{ deleted: boolean }>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: { deleted: boolean };
        }>(`/portfolios/${id}`, {
          method: 'DELETE',
        });

        return {
          data: response.data,
          message: response.message || 'Portfolio deleted successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for deletePortfolio:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }

      console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: { deleted: true },
          message: 'Portfolio deleted successfully (mock data)',
          success: true,
        });
      }, 500);
    });
  },

  // Add holding to portfolio
  addHolding: async (portfolioId: string, holdingData: AddHoldingRequest): Promise<ApiResponse<Portfolio>> => {
    try {
      if (USE_AWS_API) {
        const response = await apiRequest<{
          success: boolean;
          message: string;
          data: any;
        }>(`/portfolios/${portfolioId}/holdings`, {
          method: 'POST',
          body: JSON.stringify(holdingData),
        });

        return {
          data: response.data,
          message: response.message || 'Holding added successfully',
          success: response.success,
        };
      }
    } catch (error) {
      console.error('❌ AWS API failed for addHolding:', error);

      if (!ENABLE_MOCK_FALLBACK) {
        console.error('🚫 Mock portfolio data fallback disabled (ENABLE_MOCK_FALLBACK=false). Throwing actual error.');
        throw error;
      }

      console.warn('🔄 Falling back to mock portfolio data due to API failure (ENABLE_MOCK_FALLBACK=true)');
    }

    // Fallback to mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const portfolios = generateMockPortfolios();
          const portfolio = portfolios.find(p => p.portfolioId === portfolioId);

          if (!portfolio) {
            reject(new Error('Portfolio not found'));
            return;
          }

          // Get fund details for the holding
          const funds = generateMockFunds();
          const fund = funds.find(f => f.id === holdingData.fundId);

          if (!fund) {
            reject(new Error('Fund not found'));
            return;
          }

          const marketValue = holdingData.shares * holdingData.purchasePrice;
          const costBasis = marketValue + (holdingData.fees || 0);

          const newHolding: PortfolioHolding = {
            fundId: holdingData.fundId,
            fundName: fund.name,
            fundSymbol: fund.symbol,
            shares: holdingData.shares,
            averageCost: holdingData.purchasePrice,
            currentPrice: fund.nav,
            marketValue: holdingData.shares * fund.nav,
            costBasis,
            unrealizedGainLoss: (holdingData.shares * fund.nav) - costBasis,
            unrealizedGainLossPct: ((holdingData.shares * fund.nav) - costBasis) / costBasis * 100,
            weight: 0, // Will be recalculated
            firstPurchaseDate: holdingData.purchaseDate,
            lastUpdated: new Date().toISOString(),
          };

          portfolio.holdings.push(newHolding);
          portfolio.updatedAt = new Date().toISOString();

          resolve({
            data: portfolio,
            message: 'Holding added successfully (mock data)',
            success: true,
          });
        } catch (error) {
          reject(new Error('Failed to add holding'));
        }
      }, 800);
    });
  },
};