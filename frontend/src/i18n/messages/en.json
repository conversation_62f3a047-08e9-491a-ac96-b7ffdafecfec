{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "view": "View", "create": "Create", "update": "Update", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "refresh": "Refresh", "add": "Add", "remove": "Remove", "yes": "Yes", "no": "No", "confirm": "Confirm", "required": "Required", "readMore": "Read More"}, "navigation": {"home": "HOME", "dashboard": "DASHBOARD", "funds": "FUNDS", "portfolios": "PORTFOLIOS", "fundCompare": "FUND COMPARE", "reports": "REPORTS", "settings": "SETTINGS", "signIn": "Sign In", "signOut": "Sign Out"}, "home": {"title": "Welcome to FundFlow", "subtitle": "Take control of your financial future with our comprehensive fund management platform. Track investments, manage budgets, and generate insightful reports all in one place.", "welcomeBack": "Welcome back, {name}!", "readyToManage": "Ready to manage your funds effectively?", "goToDashboard": "Go to Dashboard", "transformFinancial": "Ready to Transform Your Financial Management?", "joinThousands": "Join thousands of users who trust FundFlow for their investment management needs.", "features": {"realTimeDashboard": {"title": "Real-time Dashboard", "description": "Get instant insights into your financial portfolio with real-time data visualization and analytics."}, "secureCompliant": {"title": "Secure & Compliant", "description": "Bank-grade security with multi-factor authentication and full compliance with financial regulations."}, "advancedAnalytics": {"title": "Advanced Analytics", "description": "Generate comprehensive reports and gain deep insights into your investment performance."}, "transactionManagement": {"title": "Transaction Management", "description": "Track and categorize all your financial transactions with smart automation and filtering."}, "budgetPlanning": {"title": "Budget Planning", "description": "Set and monitor budgets with intelligent alerts and recommendations for better financial health."}, "customizable": {"title": "Customizable", "description": "Tailor the platform to your needs with flexible settings and personalized workflows."}}, "quickActions": {"dashboard": "View your financial overview", "funds": "Explore investment funds", "portfolios": "Manage your portfolios", "transactions": "Track your transactions", "reports": "Generate insightful reports"}}, "dashboard": {"title": "Dashboard", "subtitle": "Your financial overview", "totalBalance": "Total Balance", "monthlyIncome": "Monthly Income", "monthlyExpenses": "Monthly Expenses", "savings": "Savings", "recentTransactions": "Recent Transactions", "viewAll": "View All", "noTransactions": "No transactions found", "quickActions": "Quick Actions", "managePortfolio": "Manage Portfolio", "addTransaction": "Add Transaction", "viewReports": "View Reports"}, "funds": {"title": "Funds", "subtitle": "Explore and manage investment funds", "fundDetails": "Fund Details", "basicInfo": "Basic Information", "financialDetails": "Financial Details", "fundName": "Fund Name", "symbol": "Symbol", "type": "Fund Type", "category": "Category", "subCategory": "Sub Category", "riskLevel": "Risk Level", "description": "Description", "currentNAV": "Current NAV", "previousNAV": "Previous NAV", "minimumInvestment": "Minimum Investment", "expenseRatio": "Expense Ratio (%)", "aum": "Assets Under Management", "fundManager": "Fund Manager", "inceptionDate": "Inception Date", "rating": "Rating", "performance": "Performance", "allocation": "Allocation", "riskMetrics": "Risk Metrics", "marketData": "Market Data", "createFund": "Create New Fund", "editFund": "Edit Fund", "addToPortfolio": "Add to Portfolio", "invest": "Invest", "actions": "Actions", "filters": {"title": "Filters", "fundType": "Fund Type", "category": "Category", "riskLevel": "Risk Level", "minimumRating": "Minimum Rating", "anyRating": "Any Rating", "sortBy": "Sort By", "name": "Name", "nav": "NAV", "returns": "Returns", "clearFilters": "Clear Filters"}, "types": {"mutual_fund": "Mutual Fund", "etf": "ETF", "index_fund": "Index Fund", "bond_fund": "Bond Fund", "money_market": "Money Market"}, "riskLevels": {"low": "Low", "medium": "Medium", "high": "High"}, "validation": {"nameRequired": "Fund name is required", "nameMaxLength": "Fund name must be less than 200 characters", "symbolRequired": "Fund symbol is required", "symbolMaxLength": "Fund symbol must be less than 20 characters", "symbolInvalid": "Symbol can only contain letters, numbers, dots, hyphens, and underscores", "typeRequired": "Fund type is required", "categoryRequired": "Category is required", "categoryMaxLength": "Category must be less than 100 characters", "subCategoryMaxLength": "Sub-category must be less than 100 characters", "descriptionMaxLength": "Description must be less than 1000 characters", "fundManagerRequired": "Fund manager is required", "fundManagerMaxLength": "Fund manager name must be less than 100 characters", "riskLevelRequired": "Risk level is required", "fixValidationErrors": "Please fix validation errors before submitting", "unsavedChanges": "Unsaved changes", "noChangesMade": "No changes made"}, "form": {"enterInfo": "Enter the fund information below. Required fields are marked with an asterisk (*).", "updateInfo": "Update the fund information below. Required fields are marked with an asterisk (*)."}, "manager": "Fund Manager", "fundPortfolio": "Fund Portfolio", "fundsCount": "{count} funds", "refreshData": "Refresh Data", "refreshing": "Refreshing...", "addFund": "Add Fund", "lastUpdated": "Last updated: {date}", "errorLoadingFunds": "Error Loading Funds", "retry": "Retry", "loadingFunds": "Loading fund details...", "fundNotFound": "Fund Not Found", "fundNotFoundMessage": "The requested fund could not be found.", "backToFunds": "Back to Funds", "goBack": "Go Back", "currentPerformance": "Current Performance", "change": "Change", "inputMarketData": "Input Market Data", "addToPortfolioAction": "Add to Portfolio", "editFundTitle": "Edit Fund: {name}", "currentFundInformation": "Current Fund Information", "referenceFundInfo": "Reference information for the fund you're editing.", "tryAgain": "Try Again", "fundUpdatedSuccessfully": "Fund updated successfully!", "errorUpdatingFund": "Error updating fund: {error}", "currentReferenceFund": "Current Fund Information", "marketDataInputTitle": "Market Data Input", "submitMarketDataFor": "Submit market data for {name} ({symbol})", "backToFundDetails": "Back to Fund Details", "submittingMarketData": "Submitting Market Data", "pleaseWait": "Please wait...", "marketDataSubmittedSuccessfully": "Market data submitted successfully!", "redirectingToFundDetails": "Redirecting to fund details...", "performanceVsBenchmark": "Performance vs Benchmark ({benchmarkName})", "benchmark": "Benchmark", "outperformance": "Outperformance", "keyPerformanceIndicators": "Key Performance Indicators", "realTimeMarketData": "Real-time Market Data", "advancedRiskAnalytics": "Advanced Risk Analytics", "comprehensiveHistoricalAnalysis": "Comprehensive Historical Analysis", "interactiveAllocationCharts": "Interactive Allocation Charts", "assetGeographicAllocation": "Asset & Geographic Allocation", "oneMonth": "One Month", "threeMonths": "Three Months", "sixMonths": "Six Months", "oneYear": "One Year", "threeYears": "Three Years", "fiveYears": "Five Years", "fundRating": "Fund rating: {rating} out of 5 stars", "exploreAnalyze": "Explore and analyze investment funds with advanced filtering and sorting.", "compare": "Compare", "compareSelected": "Compare Selected", "selectFunds": "Select funds to compare", "maxFundsSelected": "Maximum 3 funds can be selected for comparison", "selectUpTo3Funds": "Select up to 3 funds to compare", "noFundsSelected": "No funds selected for comparison", "fundComparison": "Fund Comparison", "comparisonTable": "Comparison Table", "fees": "Fees & Costs", "nav": "Net Asset Value", "oneYearReturn": "1 Year Return", "threeYearReturn": "3 Year Return", "fiveYearReturn": "5 Year Return", "volatility": "Volatility", "sharpeRatio": "<PERSON>", "maxDrawdown": "Max Drawdown", "closeComparison": "Close Comparison", "createNewFund": "Create New Fund", "createNewFundDescription": "Fill in the details to create a new fund in the system.", "fundCreatedSuccessfully": "\"{fundName}\" has been created successfully!", "errorCreatingFund": "Error creating fund: {error}", "overview": "Overview", "performanceAnalytics": "Performance & Analytics", "holdingsAllocation": "Holdings & Allocation", "newsFeed": "News Feed", "noNewsAvailable": "No news available at this time", "documents": "Documents", "documentsSection": {"title": "Documents", "prospectus": "Prospectus", "annualReport": "Annual Reports", "quarterlyReport": "Quarterly Reports", "factSheet": "Fact Sheets", "other": "Other Documents", "download": "Download", "noDocumentsAvailable": "No documents available at this time"}}, "portfolios": {"title": "Portfolios", "subtitle": "Manage your investment portfolios", "createPortfolio": "Create Portfolio", "editPortfolio": "Edit Portfolio", "portfolioName": "Portfolio Name", "totalValue": "Total Value", "allocation": "Allocation", "performance": "Performance", "holdings": "Holdings"}, "auth": {"signIn": "Sign In", "signOut": "Sign Out", "signInTitle": "Sign in to your account", "signInSubtitle": "Choose your preferred sign in method", "authenticationRequired": "Authentication Required", "signInRequired": "You need to sign in to access this page.", "loadingAuthentication": "Loading authentication...", "signInWith": "Sign in with {provider}", "errors": {"configuration": "There is a problem with the server configuration. Please contact support.", "accessDenied": "Access denied. You do not have permission to sign in.", "verification": "The verification token has expired or has already been used.", "default": "An error occurred during sign in. Please try again.", "serverConfiguration": "Server Configuration Error", "serverConfigDescription": "There is a problem with the authentication configuration. Please contact support.", "serverConfigSuggestion": "Try refreshing the page or contact our support team if the problem persists.", "accessDeniedTitle": "Access Denied", "accessDeniedDescription": "You do not have permission to access this resource.", "accessDeniedSuggestion": "Please make sure you have the correct permissions or contact an administrator.", "verificationTitle": "Verification Error", "verificationDescription": "The verification link has expired or has already been used.", "verificationSuggestion": "Please request a new verification link and try again.", "authenticationError": "Authentication Error", "authenticationErrorDescription": "An unexpected error occurred during authentication.", "authenticationErrorSuggestion": "Please try signing in again. If the problem continues, contact support."}}, "timePeriods": {"1D": "1 Day", "1W": "1 Week", "1M": "1 Month", "3M": "3 Months", "6M": "6 Months", "1Y": "1 Year", "3Y": "3 Years", "5Y": "5 Years", "selectTimePeriod": "Select time period for analysis"}, "marketData": {"title": "Market Data Input", "valuationMetrics": "Valuation Metrics", "riskMetrics": "Risk Metrics", "pbRatio": "P/B Ratio", "peRatio": "P/E Ratio", "dividendYield": "Dividend Yield (%)", "volatility": "Volatility (%)", "beta": "Beta", "sharpeRatio": "<PERSON>", "validated": "Mark this data as validated", "validationNotes": "Validation Notes", "priceToBookRatio": "Price-to-Book ratio", "priceToEarningsRatio": "Price-to-Earnings ratio", "dividendYieldPercentage": "Dividend yield percentage", "volatilityPercentage": "Volatility percentage", "validationNotesPlaceholder": "Validation notes..."}, "theme": {"toggleTheme": "Toggle theme", "light": "Light", "dark": "Dark"}, "language": {"switchLanguage": "Switch Language", "currentLanguage": "Current Language"}}