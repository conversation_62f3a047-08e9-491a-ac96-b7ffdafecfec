'use client';

import { useRouter } from 'next/navigation';
import { useTranslation } from '@/i18n/provider';
import { Card, Button } from '@/components/ui';
import { Fund } from '@/types';
import { safeINR } from '@/utils';
import {
  CheckCircle,
  Error,
  Warning,
  PictureAsPdf,
  Edit,
  Visibility,
  Refresh,
} from '@mui/icons-material';

export interface UploadResult {
  id: string;
  fileName: string;
  status: 'processing' | 'success' | 'error' | 'warning';
  message: string;
  fundData?: Fund;
  error?: string;
  warnings?: string[];
  processingTime?: number;
}

interface UploadStatusProps {
  results: UploadResult[];
  onRetry?: (resultId: string) => void;
  onClear?: () => void;
  className?: string;
}

const UploadStatus: React.FC<UploadStatusProps> = ({
  results,
  onRetry,
  onClear,
  className = '',
}) => {
  const { t } = useTranslation();
  const router = useRouter();

  const getStatusIcon = (status: UploadResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'error':
        return <Error className="w-6 h-6 text-red-500" />;
      case 'warning':
        return <Warning className="w-6 h-6 text-yellow-500" />;
      case 'processing':
        return (
          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <PictureAsPdf className="w-6 h-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: UploadResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
      case 'processing':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800';
    }
  };

  const handleViewFund = (fundData: Fund) => {
    router.push(`/funds/${fundData.id}`);
  };

  const handleEditFund = (fundData: Fund) => {
    router.push(`/funds/${fundData.id}/edit`);
  };

  const formatProcessingTime = (time?: number): string => {
    if (!time) return '';
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(1)}s`;
  };

  const getResultSummary = () => {
    const total = results.length;
    const successful = results.filter(r => r.status === 'success').length;
    const errors = results.filter(r => r.status === 'error').length;
    const warnings = results.filter(r => r.status === 'warning').length;
    const processing = results.filter(r => r.status === 'processing').length;

    return { total, successful, errors, warnings, processing };
  };

  const summary = getResultSummary();

  if (results.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Card */}
      <Card>
        <Card.Header>
          <div className="flex items-center justify-between">
            <div>
              <Card.Title>Upload Results</Card.Title>
              <Card.Description>
                {summary.total} file{summary.total !== 1 ? 's' : ''} processed
              </Card.Description>
            </div>
            {onClear && summary.processing === 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClear}
                className="flex items-center space-x-2"
              >
                <span>Clear Results</span>
              </Button>
            )}
          </div>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {summary.successful}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">
                Successful
              </div>
            </div>
            <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {summary.errors}
              </div>
              <div className="text-sm text-red-600 dark:text-red-400">
                Errors
              </div>
            </div>
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {summary.warnings}
              </div>
              <div className="text-sm text-yellow-600 dark:text-yellow-400">
                Warnings
              </div>
            </div>
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {summary.processing}
              </div>
              <div className="text-sm text-blue-600 dark:text-blue-400">
                Processing
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Results List */}
      <div className="space-y-3">
        {results.map((result) => (
          <div
            key={result.id}
            className={`
              border rounded-lg p-4 transition-colors duration-200
              ${getStatusColor(result.status)}
            `}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1 min-w-0">
                {getStatusIcon(result.status)}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {result.fileName}
                    </h4>
                    {result.processingTime && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        ({formatProcessingTime(result.processingTime)})
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {result.message}
                  </p>

                  {/* Fund Data Preview */}
                  {result.fundData && (
                    <div className="mt-3 p-3 bg-white dark:bg-gray-900 rounded-md border border-gray-200 dark:border-gray-700">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">
                            Fund Name:
                          </span>
                          <span className="ml-2 text-gray-900 dark:text-gray-100">
                            {result.fundData.name}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">
                            NAV:
                          </span>
                          <span className="ml-2 text-gray-900 dark:text-gray-100">
                            {safeINR(result.fundData.nav)}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700 dark:text-gray-300">
                            Type:
                          </span>
                          <span className="ml-2 text-gray-900 dark:text-gray-100">
                            {result.fundData.type.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Warnings */}
                  {result.warnings && result.warnings.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {result.warnings.map((warning, index) => (
                        <div
                          key={index}
                          className="text-xs text-yellow-700 dark:text-yellow-300 flex items-center space-x-1"
                        >
                          <Warning className="w-3 h-3" />
                          <span>{warning}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Error Details */}
                  {result.error && (
                    <div className="mt-2 text-xs text-red-700 dark:text-red-300 flex items-center space-x-1">
                      <Error className="w-3 h-3" />
                      <span>{result.error}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2 ml-4">
                {result.status === 'success' && result.fundData && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewFund(result.fundData!)}
                      className="flex items-center space-x-1"
                    >
                      <Visibility className="w-4 h-4" />
                      <span>View</span>
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleEditFund(result.fundData!)}
                      className="flex items-center space-x-1"
                    >
                      <Edit className="w-4 h-4" />
                      <span>Edit</span>
                    </Button>
                  </>
                )}
                
                {result.status === 'error' && onRetry && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRetry(result.id)}
                    className="flex items-center space-x-1"
                  >
                    <Refresh className="w-4 h-4" />
                    <span>Retry</span>
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UploadStatus;
